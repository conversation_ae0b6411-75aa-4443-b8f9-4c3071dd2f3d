"""
AI场次分割服务
"""
import json
import asyncio
from typing import List, Dict, Any, Optional
from decimal import Decimal
from sqlalchemy.orm import Session
import google.generativeai as genai
from datetime import datetime

from app.models.video import Video
from app.models.scene import Scene
from app.models.scene import Subtitle
from app.config.settings import get_settings

settings = get_settings()

class AISceneService:
    """AI场次分割服务"""
    
    def __init__(self, db: Session):
        self.db = db
        # 配置Gemini API
        if hasattr(settings, 'GEMINI_API_KEY') and settings.GEMINI_API_KEY:
            genai.configure(api_key=settings.GEMINI_API_KEY)
            self.model = genai.GenerativeModel('gemini-pro')
        else:
            self.model = None
            print("Warning: GEMINI_API_KEY not configured, using mock data")
    
    async def process_video_scenes(self, video_id: str) -> Dict[str, Any]:
        """
        处理视频场次分割
        1. 获取视频字幕
        2. 调用Gemini进行场次分析
        3. 保存场次数据到数据库
        """
        try:
            # 获取视频信息
            video = self.db.query(Video).filter(Video.id == video_id).first()
            if not video:
                raise Exception(f"视频不存在: {video_id}")
            
            # 获取字幕数据
            subtitles = self.db.query(Subtitle).filter(
                Subtitle.video_id == video_id
            ).order_by(Subtitle.start_time).all()
            
            if not subtitles:
                raise Exception("视频字幕不存在，请先进行语音识别")
            
            # 构建字幕文本
            subtitle_text = self._build_subtitle_text(subtitles)
            
            # 调用AI进行场次分析
            scenes_data = await self._analyze_scenes_with_ai(subtitle_text, subtitles)
            
            # 保存场次到数据库
            created_scenes = []
            for scene_data in scenes_data:
                scene = Scene(
                    video_id=video_id,
                    start_time=Decimal(str(scene_data['start_time'])),
                    end_time=Decimal(str(scene_data['end_time'])),
                    title=scene_data['title'],
                    description=scene_data['description'],
                    characters=scene_data['characters'],
                    props=scene_data['props'],
                    assets=scene_data['assets'],
                    tags=scene_data['tags'],
                    location=scene_data['location'],
                    time_of_day=scene_data.get('time_of_day'),
                    weather=scene_data.get('weather'),
                    mood=scene_data['mood'],
                    action_type=scene_data['action_type'],
                    dialogue_summary=scene_data['dialogue_summary'],
                    visual_description=scene_data['visual_description'],
                    ai_generated=True,
                    human_verified=False,
                    confidence_score=Decimal(str(scene_data.get('confidence_score', 0.8))),
                    scene_metadata={
                        'ai_model': 'gemini-pro',
                        'processed_at': datetime.utcnow().isoformat(),
                        'subtitle_count': len(subtitles)
                    }
                )
                
                self.db.add(scene)
                created_scenes.append(scene)
            
            self.db.commit()
            
            # 刷新对象以获取ID
            for scene in created_scenes:
                self.db.refresh(scene)
            
            return {
                "status": "success",
                "scenes_created": len(created_scenes),
                "scenes": [{"id": str(scene.id), "title": scene.title} for scene in created_scenes]
            }
            
        except Exception as e:
            self.db.rollback()
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _build_subtitle_text(self, subtitles: List[Subtitle]) -> str:
        """构建用于AI分析的字幕文本"""
        lines = []
        for subtitle in subtitles:
            timestamp = f"[{subtitle.start_time:.2f}s-{subtitle.end_time:.2f}s]"
            speaker = f"({subtitle.speaker})" if subtitle.speaker else ""
            lines.append(f"{timestamp} {speaker} {subtitle.text}")
        return "\n".join(lines)
    
    async def _analyze_scenes_with_ai(self, subtitle_text: str, subtitles: List[Subtitle]) -> List[Dict[str, Any]]:
        """使用AI分析场次"""
        if not self.model:
            # 返回模拟数据用于测试
            return self._generate_mock_scenes(subtitles)
        
        prompt = f"""
请分析以下视频字幕内容，将其分割成不同的场次。每个场次应该包含以下信息：

字幕内容：
{subtitle_text}

请返回JSON格式的场次分析结果，包含以下字段：
- start_time: 场次开始时间（秒）
- end_time: 场次结束时间（秒）
- title: 场次标题（简洁描述）
- description: 场次详细描述
- characters: 出现的人物列表
- props: 道具资产列表
- assets: 场景资产列表
- tags: 标签列表
- location: 地点
- time_of_day: 时间段（如：白天、夜晚、黄昏等）
- weather: 天气（如果提及）
- mood: 情绪氛围
- action_type: 动作类型（如：对话、动作、独白等）
- dialogue_summary: 对话摘要
- visual_description: 视觉描述
- confidence_score: 分析置信度（0-1）

请确保返回的是有效的JSON数组格式。
"""
        
        try:
            response = await asyncio.to_thread(self.model.generate_content, prompt)
            
            # 解析AI响应
            response_text = response.text.strip()
            
            # 尝试提取JSON内容
            if response_text.startswith('```json'):
                response_text = response_text[7:-3]
            elif response_text.startswith('```'):
                response_text = response_text[3:-3]
            
            scenes_data = json.loads(response_text)
            
            # 验证和清理数据
            return self._validate_scenes_data(scenes_data, subtitles)
            
        except Exception as e:
            print(f"AI分析失败，使用模拟数据: {e}")
            return self._generate_mock_scenes(subtitles)
    
    def _validate_scenes_data(self, scenes_data: List[Dict], subtitles: List[Subtitle]) -> List[Dict[str, Any]]:
        """验证和清理AI返回的场次数据"""
        validated_scenes = []
        
        for scene in scenes_data:
            # 确保必需字段存在
            validated_scene = {
                'start_time': float(scene.get('start_time', 0)),
                'end_time': float(scene.get('end_time', 60)),
                'title': scene.get('title', '未命名场次'),
                'description': scene.get('description', ''),
                'characters': scene.get('characters', []),
                'props': scene.get('props', []),
                'assets': scene.get('assets', []),
                'tags': scene.get('tags', []),
                'location': scene.get('location', ''),
                'time_of_day': scene.get('time_of_day'),
                'weather': scene.get('weather'),
                'mood': scene.get('mood', ''),
                'action_type': scene.get('action_type', '对话'),
                'dialogue_summary': scene.get('dialogue_summary', ''),
                'visual_description': scene.get('visual_description', ''),
                'confidence_score': min(max(float(scene.get('confidence_score', 0.8)), 0), 1)
            }
            
            validated_scenes.append(validated_scene)
        
        return validated_scenes
    
    def _generate_mock_scenes(self, subtitles: List[Subtitle]) -> List[Dict[str, Any]]:
        """生成模拟场次数据用于测试"""
        if not subtitles:
            return []
        
        # 简单按时间分割场次
        total_duration = float(subtitles[-1].end_time)
        scene_duration = max(60, total_duration / 3)  # 每个场次至少60秒
        
        scenes = []
        current_time = 0
        scene_num = 1
        
        while current_time < total_duration:
            end_time = min(current_time + scene_duration, total_duration)
            
            # 获取该时间段的字幕
            scene_subtitles = [
                s for s in subtitles 
                if s.start_time >= current_time and s.end_time <= end_time
            ]
            
            dialogue_text = " ".join([s.text for s in scene_subtitles])
            
            scene = {
                'start_time': current_time,
                'end_time': end_time,
                'title': f'场次 {scene_num}',
                'description': f'第{scene_num}个场次的内容',
                'characters': ['角色A', '角色B'],
                'props': ['道具1'],
                'assets': ['场景1'],
                'tags': ['对话', '剧情'],
                'location': '室内',
                'time_of_day': '白天',
                'weather': None,
                'mood': '平静',
                'action_type': '对话',
                'dialogue_summary': dialogue_text[:200] + '...' if len(dialogue_text) > 200 else dialogue_text,
                'visual_description': '场景的视觉描述',
                'confidence_score': 0.7
            }
            
            scenes.append(scene)
            current_time = end_time
            scene_num += 1
        
        return scenes
