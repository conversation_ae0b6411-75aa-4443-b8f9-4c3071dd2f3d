"""
场次管理路由
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID

from app.database.connection import get_db
from app.controllers.scene_controller import SceneController
from app.models.schemas import SceneCreate, SceneUpdate, SceneResponse

router = APIRouter()

def get_scene_controller(db: Session = Depends(get_db)) -> SceneController:
    """获取场次控制器实例"""
    return SceneController(db)

@router.post("/scenes", response_model=SceneResponse)
async def create_scene(
    scene_data: SceneCreate,
    user_id: UUID = Query(..., description="用户ID"),
    controller: SceneController = Depends(get_scene_controller)
):
    """创建场次"""
    try:
        scene = controller.create_scene(scene_data, user_id)
        return SceneResponse.model_validate(scene)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建场次失败: {str(e)}")

@router.get("/scenes/{scene_id}", response_model=SceneResponse)
async def get_scene(
    scene_id: UUID,
    user_id: UUID = Query(..., description="用户ID"),
    controller: SceneController = Depends(get_scene_controller)
):
    """获取场次详情"""
    scene = controller.get_scene(scene_id, user_id)
    if not scene:
        raise HTTPException(status_code=404, detail="场次不存在")
    return SceneResponse.model_validate(scene)

@router.put("/scenes/{scene_id}", response_model=SceneResponse)
async def update_scene(
    scene_id: UUID,
    scene_data: SceneUpdate,
    user_id: UUID = Query(..., description="用户ID"),
    controller: SceneController = Depends(get_scene_controller)
):
    """更新场次"""
    scene = controller.update_scene(scene_id, scene_data, user_id)
    if not scene:
        raise HTTPException(status_code=404, detail="场次不存在")
    return SceneResponse.model_validate(scene)

@router.delete("/scenes/{scene_id}")
async def delete_scene(
    scene_id: UUID,
    user_id: UUID = Query(..., description="用户ID"),
    controller: SceneController = Depends(get_scene_controller)
):
    """删除场次"""
    success = controller.delete_scene(scene_id, user_id)
    if not success:
        raise HTTPException(status_code=404, detail="场次不存在")
    return {"message": "场次删除成功"}

@router.get("/videos/{video_id}/scenes", response_model=List[SceneResponse])
async def get_video_scenes(
    video_id: UUID,
    user_id: UUID = Query(..., description="用户ID"),
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(100, ge=1, le=100, description="返回数量"),
    controller: SceneController = Depends(get_scene_controller)
):
    """获取视频的所有场次"""
    scenes = controller.get_video_scenes(video_id, user_id, skip, limit)
    return [SceneResponse.model_validate(scene) for scene in scenes]

@router.get("/projects/{project_id}/scenes", response_model=List[SceneResponse])
async def get_project_scenes(
    project_id: UUID,
    user_id: UUID = Query(..., description="用户ID"),
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(100, ge=1, le=100, description="返回数量"),
    controller: SceneController = Depends(get_scene_controller)
):
    """获取项目的所有场次"""
    scenes = controller.get_project_scenes(project_id, user_id, skip, limit)
    return [SceneResponse.model_validate(scene) for scene in scenes]

@router.post("/scenes/{scene_id}/verify")
async def verify_scene(
    scene_id: UUID,
    user_id: UUID = Query(..., description="用户ID"),
    controller: SceneController = Depends(get_scene_controller)
):
    """人工验证场次"""
    scene = controller.get_scene(scene_id, user_id)
    if not scene:
        raise HTTPException(status_code=404, detail="场次不存在")

    # 标记为人工验证
    scene.human_verified = True
    controller.db.commit()

    return {"message": "场次验证成功", "scene_id": str(scene_id)}

@router.get("/scenes/search", response_model=List[SceneResponse])
async def search_scenes(
    q: str = Query(..., description="搜索关键词"),
    project_id: Optional[UUID] = Query(None, description="项目ID"),
    user_id: UUID = Query(..., description="用户ID"),
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(50, ge=1, le=100, description="返回数量"),
    controller: SceneController = Depends(get_scene_controller)
):
    """搜索场次"""
    scenes = controller.search_scenes(q, project_id, user_id, skip, limit)
    return [SceneResponse.model_validate(scene) for scene in scenes]

@router.get("/videos/{video_id}/scenes/time-range", response_model=List[SceneResponse])
async def get_scenes_by_time_range(
    video_id: UUID,
    start_time: float = Query(..., description="开始时间（秒）"),
    end_time: float = Query(..., description="结束时间（秒）"),
    user_id: UUID = Query(..., description="用户ID"),
    controller: SceneController = Depends(get_scene_controller)
):
    """根据时间范围获取场次"""
    scenes = controller.get_scenes_by_time_range(video_id, start_time, end_time, user_id)
    return [SceneResponse.model_validate(scene) for scene in scenes]

@router.get("/scenes/by-characters", response_model=List[SceneResponse])
async def get_scenes_by_characters(
    characters: str = Query(..., description="人物名称，多个用逗号分隔"),
    project_id: Optional[UUID] = Query(None, description="项目ID"),
    user_id: UUID = Query(..., description="用户ID"),
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(50, ge=1, le=100, description="返回数量"),
    controller: SceneController = Depends(get_scene_controller)
):
    """根据人物获取场次"""
    character_list = [c.strip() for c in characters.split(",") if c.strip()]
    scenes = controller.get_scenes_by_characters(character_list, project_id, user_id, skip, limit)
    return [SceneResponse.model_validate(scene) for scene in scenes]

@router.get("/scenes/by-tags", response_model=List[SceneResponse])
async def get_scenes_by_tags(
    tags: str = Query(..., description="标签，多个用逗号分隔"),
    project_id: Optional[UUID] = Query(None, description="项目ID"),
    user_id: UUID = Query(..., description="用户ID"),
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(50, ge=1, le=100, description="返回数量"),
    controller: SceneController = Depends(get_scene_controller)
):
    """根据标签获取场次"""
    tag_list = [t.strip() for t in tags.split(",") if t.strip()]
    scenes = controller.get_scenes_by_tags(tag_list, project_id, user_id, skip, limit)
    return [SceneResponse.model_validate(scene) for scene in scenes]

@router.get("/projects/{project_id}/scenes/stats")
async def get_scene_stats(
    project_id: UUID,
    user_id: UUID = Query(..., description="用户ID"),
    controller: SceneController = Depends(get_scene_controller)
):
    """获取项目场次统计"""
    stats = controller.get_scene_stats(project_id, user_id)
    if not stats:
        raise HTTPException(status_code=404, detail="项目不存在或无场次数据")
    return stats

# 场次收藏相关路由
from app.controllers.scene_favorite_controller import SceneFavoriteController
from app.models.schemas import SceneFavoriteCreate, SceneFavoriteUpdate, SceneFavoriteResponse

def get_scene_favorite_controller(db: Session = Depends(get_db)) -> SceneFavoriteController:
    """获取场次收藏控制器实例"""
    return SceneFavoriteController(db)

@router.post("/scenes/{scene_id}/favorite", response_model=SceneFavoriteResponse)
async def add_scene_to_favorites(
    scene_id: UUID,
    favorite_data: SceneFavoriteCreate,
    user_id: UUID = Query(..., description="用户ID"),
    controller: SceneFavoriteController = Depends(get_scene_favorite_controller)
):
    """收藏场次"""
    try:
        # 设置场次ID
        favorite_data.scene_id = scene_id
        favorite = controller.add_favorite(favorite_data, user_id)
        return SceneFavoriteResponse.model_validate(favorite)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"收藏失败: {str(e)}")

@router.delete("/scenes/{scene_id}/favorite")
async def remove_scene_from_favorites(
    scene_id: UUID,
    user_id: UUID = Query(..., description="用户ID"),
    controller: SceneFavoriteController = Depends(get_scene_favorite_controller)
):
    """取消收藏场次"""
    success = controller.remove_scene_favorite(scene_id, user_id)
    if not success:
        raise HTTPException(status_code=404, detail="收藏不存在")
    return {"message": "取消收藏成功"}

@router.get("/scenes/{scene_id}/favorite", response_model=SceneFavoriteResponse)
async def get_scene_favorite(
    scene_id: UUID,
    user_id: UUID = Query(..., description="用户ID"),
    controller: SceneFavoriteController = Depends(get_scene_favorite_controller)
):
    """获取场次收藏信息"""
    favorite = controller.get_scene_favorite(scene_id, user_id)
    if not favorite:
        raise HTTPException(status_code=404, detail="收藏不存在")
    return SceneFavoriteResponse.model_validate(favorite)

@router.get("/favorites", response_model=List[SceneFavoriteResponse])
async def get_user_favorites(
    user_id: UUID = Query(..., description="用户ID"),
    project_id: Optional[UUID] = Query(None, description="项目ID"),
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(100, ge=1, le=100, description="返回数量"),
    controller: SceneFavoriteController = Depends(get_scene_favorite_controller)
):
    """获取用户收藏列表"""
    favorites = controller.get_user_favorites(user_id, project_id, skip, limit)
    return [SceneFavoriteResponse.model_validate(favorite) for favorite in favorites]

@router.put("/favorites/{favorite_id}", response_model=SceneFavoriteResponse)
async def update_scene_favorite(
    favorite_id: UUID,
    favorite_data: SceneFavoriteUpdate,
    user_id: UUID = Query(..., description="用户ID"),
    controller: SceneFavoriteController = Depends(get_scene_favorite_controller)
):
    """更新收藏备注"""
    favorite = controller.update_favorite(favorite_id, favorite_data, user_id)
    if not favorite:
        raise HTTPException(status_code=404, detail="收藏不存在")
    return SceneFavoriteResponse.model_validate(favorite)

@router.get("/favorites/search", response_model=List[SceneFavoriteResponse])
async def search_favorites(
    q: str = Query(..., description="搜索关键词"),
    user_id: UUID = Query(..., description="用户ID"),
    project_id: Optional[UUID] = Query(None, description="项目ID"),
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(50, ge=1, le=100, description="返回数量"),
    controller: SceneFavoriteController = Depends(get_scene_favorite_controller)
):
    """搜索收藏"""
    favorites = controller.search_favorites(q, user_id, project_id, skip, limit)
    return [SceneFavoriteResponse.model_validate(favorite) for favorite in favorites]

@router.get("/favorites/stats")
async def get_favorite_stats(
    user_id: UUID = Query(..., description="用户ID"),
    project_id: Optional[UUID] = Query(None, description="项目ID"),
    controller: SceneFavoriteController = Depends(get_scene_favorite_controller)
):
    """获取收藏统计"""
    stats = controller.get_favorite_stats(user_id, project_id)
    if not stats:
        raise HTTPException(status_code=404, detail="无收藏数据")
    return stats
