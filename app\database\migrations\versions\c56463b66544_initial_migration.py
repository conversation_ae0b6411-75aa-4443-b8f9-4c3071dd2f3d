"""Initial migration

Revision ID: c56463b66544
Revises: 
Create Date: 2025-08-02 23:25:08.841593

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'c56463b66544'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('projects',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=255), nullable=False, comment='项目名称'),
    sa.Column('description', sa.Text(), nullable=True, comment='项目描述'),
    sa.Column('user_id', sa.UUID(), nullable=False, comment='用户ID'),
    sa.Column('resource_path', sa.String(length=500), nullable=False, comment='项目资源目录路径'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
    sa.Column('status', sa.String(length=50), nullable=True, comment='项目状态'),
    sa.Column('settings', sa.JSON(), nullable=True, comment='项目设置'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('videos',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('project_id', sa.UUID(), nullable=False, comment='项目ID'),
    sa.Column('filename', sa.String(length=255), nullable=False, comment='文件名'),
    sa.Column('relative_path', sa.String(length=500), nullable=False, comment='相对于项目目录的路径'),
    sa.Column('file_size', sa.BigInteger(), nullable=False, comment='文件大小(字节)'),
    sa.Column('duration', sa.Numeric(precision=10, scale=3), nullable=False, comment='视频时长(秒)'),
    sa.Column('width', sa.Integer(), nullable=True, comment='视频宽度'),
    sa.Column('height', sa.Integer(), nullable=True, comment='视频高度'),
    sa.Column('fps', sa.Numeric(precision=8, scale=3), nullable=True, comment='帧率'),
    sa.Column('format', sa.String(length=50), nullable=True, comment='视频格式'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
    sa.Column('last_scanned', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='最后扫描时间'),
    sa.Column('processing_status', sa.String(length=50), nullable=True, comment='处理状态'),
    sa.Column('video_metadata', sa.JSON(), nullable=True, comment='视频元数据'),
    sa.ForeignKeyConstraint(['project_id'], ['projects.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('scenes',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('video_id', sa.UUID(), nullable=False, comment='视频ID'),
    sa.Column('scene_number', sa.Integer(), nullable=False, comment='场次编号'),
    sa.Column('start_time', sa.Numeric(precision=10, scale=3), nullable=False, comment='开始时间(秒)'),
    sa.Column('end_time', sa.Numeric(precision=10, scale=3), nullable=False, comment='结束时间(秒)'),
    sa.Column('title', sa.String(length=255), nullable=True, comment='场次标题'),
    sa.Column('description', sa.Text(), nullable=True, comment='场次描述'),
    sa.Column('location', sa.String(length=255), nullable=True, comment='拍摄地点'),
    sa.Column('time_of_day', sa.String(length=50), nullable=True, comment='时间段'),
    sa.Column('characters', sa.ARRAY(sa.Text()), nullable=True, comment='角色列表'),
    sa.Column('props', sa.ARRAY(sa.Text()), nullable=True, comment='道具列表'),
    sa.Column('assets', sa.ARRAY(sa.Text()), nullable=True, comment='资产列表'),
    sa.Column('tags', sa.ARRAY(sa.Text()), nullable=True, comment='标签列表'),
    sa.Column('mood', sa.String(length=100), nullable=True, comment='情绪氛围'),
    sa.Column('action_type', sa.String(length=100), nullable=True, comment='动作类型'),
    sa.Column('dialogue_summary', sa.Text(), nullable=True, comment='对话摘要'),
    sa.Column('visual_description', sa.Text(), nullable=True, comment='视觉描述'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
    sa.Column('ai_generated', sa.Boolean(), nullable=True, comment='是否AI生成'),
    sa.Column('human_verified', sa.Boolean(), nullable=True, comment='是否人工验证'),
    sa.Column('confidence_score', sa.Numeric(precision=3, scale=2), nullable=True, comment='置信度分数'),
    sa.Column('scene_metadata', sa.JSON(), nullable=True, comment='场次元数据'),
    sa.ForeignKeyConstraint(['video_id'], ['videos.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('subtitles',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('video_id', sa.UUID(), nullable=False, comment='视频ID'),
    sa.Column('start_time', sa.Numeric(precision=10, scale=3), nullable=False, comment='开始时间(秒)'),
    sa.Column('end_time', sa.Numeric(precision=10, scale=3), nullable=False, comment='结束时间(秒)'),
    sa.Column('text', sa.Text(), nullable=False, comment='字幕文本'),
    sa.Column('speaker', sa.String(length=255), nullable=True, comment='说话人'),
    sa.Column('confidence', sa.Numeric(precision=3, scale=2), nullable=True, comment='识别置信度'),
    sa.Column('language', sa.String(length=10), nullable=True, comment='语言'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.ForeignKeyConstraint(['video_id'], ['videos.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('scene_favorites',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('project_id', sa.UUID(), nullable=False, comment='项目ID'),
    sa.Column('scene_id', sa.UUID(), nullable=False, comment='场次ID'),
    sa.Column('user_id', sa.UUID(), nullable=False, comment='用户ID'),
    sa.Column('notes', sa.Text(), nullable=True, comment='用户备注'),
    sa.Column('tags', sa.ARRAY(sa.Text()), nullable=True, comment='用户自定义标签'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='更新时间'),
    sa.ForeignKeyConstraint(['project_id'], ['projects.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['scene_id'], ['scenes.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('project_id', 'scene_id', name='uq_project_scene_favorite')
    )
    op.create_table('vector_embeddings',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('scene_id', sa.UUID(), nullable=False, comment='场次ID'),
    sa.Column('embedding_type', sa.String(length=50), nullable=False, comment='嵌入类型: text, visual, combined'),
    sa.Column('vector_id', sa.String(length=255), nullable=False, comment='Milvus中的向量ID'),
    sa.Column('content_hash', sa.String(length=64), nullable=False, comment='内容哈希'),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True, comment='创建时间'),
    sa.ForeignKeyConstraint(['scene_id'], ['scenes.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('vector_embeddings')
    op.drop_table('scene_favorites')
    op.drop_table('subtitles')
    op.drop_table('scenes')
    op.drop_table('videos')
    op.drop_table('projects')
    # ### end Alembic commands ###
