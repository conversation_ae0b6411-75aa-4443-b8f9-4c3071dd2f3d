"""
语音识别服务
"""
import os
import json
import time
import hashlib
from typing import List, Dict, Optional, Tuple
from datetime import datetime
from sqlalchemy.orm import Session

# 火山引擎语音识别SDK（需要安装）
try:
    # 这里应该导入火山引擎的SDK
    # import volcengine_speech
    VOLCANO_AVAILABLE = False  # 暂时设为False，避免依赖问题
except ImportError:
    VOLCANO_AVAILABLE = False

from app.models.video import Video
from app.models.scene import Subtitle
from app.config.settings import settings

class SpeechRecognitionService:
    """语音识别服务"""
    
    def __init__(self, db: Session):
        self.db = db
        self.app_id = settings.volcano_app_id
        self.access_key = settings.volcano_access_key
        self.secret_key = settings.volcano_secret_key
    
    def extract_audio_from_video(self, video_path: str, output_path: str) -> bool:
        """从视频中提取音频"""
        try:
            # 使用ffmpeg提取音频
            # 这里需要ffmpeg-python库
            import ffmpeg
            
            (
                ffmpeg
                .input(video_path)
                .output(output_path, acodec='pcm_s16le', ac=1, ar='16000')
                .overwrite_output()
                .run(quiet=True)
            )
            return True
        except Exception as e:
            print(f"音频提取失败: {e}")
            return False
    
    def recognize_speech_volcano(self, audio_path: str) -> List[Dict]:
        """使用火山引擎进行语音识别"""
        if not VOLCANO_AVAILABLE:
            # 返回模拟数据用于开发测试
            return self._mock_speech_recognition()
        
        try:
            # 这里应该实现火山引擎的语音识别调用
            # 具体实现需要根据火山引擎的SDK文档
            
            # 示例代码结构：
            # client = volcengine_speech.SpeechClient(
            #     app_id=self.app_id,
            #     access_key=self.access_key,
            #     secret_key=self.secret_key
            # )
            # 
            # result = client.recognize_file(
            #     audio_path,
            #     language='zh-CN',
            #     sample_rate=16000,
            #     format='wav'
            # )
            
            # 返回模拟数据
            return self._mock_speech_recognition()
            
        except Exception as e:
            print(f"语音识别失败: {e}")
            return []
    
    def _mock_speech_recognition(self) -> List[Dict]:
        """模拟语音识别结果"""
        return [
            {
                "start_time": 0.0,
                "end_time": 3.5,
                "text": "欢迎来到我们的视频场次分割系统",
                "speaker": "speaker_1",
                "confidence": 0.95
            },
            {
                "start_time": 3.5,
                "end_time": 7.2,
                "text": "这个系统可以智能地识别视频中的不同场次",
                "speaker": "speaker_1", 
                "confidence": 0.92
            },
            {
                "start_time": 7.2,
                "end_time": 11.8,
                "text": "并且能够自动生成字幕和场次描述",
                "speaker": "speaker_2",
                "confidence": 0.88
            },
            {
                "start_time": 11.8,
                "end_time": 15.0,
                "text": "让视频内容的管理变得更加高效",
                "speaker": "speaker_2",
                "confidence": 0.91
            }
        ]
    
    def process_video_speech(self, video_id: str) -> Dict:
        """处理视频的语音识别"""
        # 获取视频信息
        video = self.db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise ValueError(f"视频 {video_id} 不存在")
        
        # 构建视频文件路径
        project = video.project
        video_path = os.path.join(project.resource_path, video.relative_path)
        
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"视频文件不存在: {video_path}")
        
        # 更新处理状态
        video.processing_status = 'speech_processing'
        self.db.commit()
        
        try:
            # 提取音频
            audio_dir = os.path.join(project.resource_path, "audio")
            os.makedirs(audio_dir, exist_ok=True)
            
            audio_filename = f"{video.id}.wav"
            audio_path = os.path.join(audio_dir, audio_filename)
            
            if not self.extract_audio_from_video(video_path, audio_path):
                raise Exception("音频提取失败")
            
            # 语音识别
            recognition_results = self.recognize_speech_volcano(audio_path)
            
            # 保存字幕到数据库
            subtitles_created = 0
            for result in recognition_results:
                subtitle = Subtitle(
                    video_id=video.id,
                    start_time=result['start_time'],
                    end_time=result['end_time'],
                    text=result['text'],
                    speaker=result.get('speaker'),
                    confidence=result.get('confidence'),
                    language='zh-CN'
                )
                self.db.add(subtitle)
                subtitles_created += 1
            
            # 更新处理状态
            video.processing_status = 'speech_completed'
            self.db.commit()
            
            # 清理临时音频文件
            if os.path.exists(audio_path):
                os.remove(audio_path)
            
            return {
                "video_id": str(video.id),
                "status": "success",
                "subtitles_count": subtitles_created,
                "total_duration": float(video.duration),
                "processed_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            # 更新处理状态为失败
            video.processing_status = 'speech_failed'
            self.db.commit()
            
            return {
                "video_id": str(video.id),
                "status": "error",
                "error": str(e),
                "processed_at": datetime.utcnow().isoformat()
            }
    
    def get_video_subtitles(self, video_id: str, start_time: Optional[float] = None, 
                           end_time: Optional[float] = None) -> List[Subtitle]:
        """获取视频字幕"""
        query = self.db.query(Subtitle).filter(Subtitle.video_id == video_id)
        
        if start_time is not None:
            query = query.filter(Subtitle.start_time >= start_time)
        
        if end_time is not None:
            query = query.filter(Subtitle.end_time <= end_time)
        
        return query.order_by(Subtitle.start_time).all()
    
    def search_subtitles(self, video_id: str, search_text: str) -> List[Subtitle]:
        """在字幕中搜索文本"""
        return self.db.query(Subtitle).filter(
            Subtitle.video_id == video_id,
            Subtitle.text.ilike(f"%{search_text}%")
        ).order_by(Subtitle.start_time).all()
    
    def export_subtitles(self, video_id: str, format: str = 'srt') -> str:
        """导出字幕文件"""
        subtitles = self.get_video_subtitles(video_id)
        
        if format.lower() == 'srt':
            return self._export_srt(subtitles)
        elif format.lower() == 'vtt':
            return self._export_vtt(subtitles)
        else:
            raise ValueError(f"不支持的字幕格式: {format}")
    
    def _export_srt(self, subtitles: List[Subtitle]) -> str:
        """导出SRT格式字幕"""
        srt_content = []
        
        for i, subtitle in enumerate(subtitles, 1):
            start_time = self._seconds_to_srt_time(float(subtitle.start_time))
            end_time = self._seconds_to_srt_time(float(subtitle.end_time))
            
            srt_content.append(f"{i}")
            srt_content.append(f"{start_time} --> {end_time}")
            srt_content.append(subtitle.text)
            srt_content.append("")  # 空行
        
        return "\n".join(srt_content)
    
    def _export_vtt(self, subtitles: List[Subtitle]) -> str:
        """导出VTT格式字幕"""
        vtt_content = ["WEBVTT", ""]
        
        for subtitle in subtitles:
            start_time = self._seconds_to_vtt_time(float(subtitle.start_time))
            end_time = self._seconds_to_vtt_time(float(subtitle.end_time))
            
            vtt_content.append(f"{start_time} --> {end_time}")
            vtt_content.append(subtitle.text)
            vtt_content.append("")  # 空行
        
        return "\n".join(vtt_content)
    
    def _seconds_to_srt_time(self, seconds: float) -> str:
        """将秒数转换为SRT时间格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)
        
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"
    
    def _seconds_to_vtt_time(self, seconds: float) -> str:
        """将秒数转换为VTT时间格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        
        return f"{hours:02d}:{minutes:02d}:{secs:06.3f}"
