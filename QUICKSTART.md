# 🚀 快速启动指南

## 📋 前置要求

确保您已安装以下软件：
- ✅ Python 3.11+
- ✅ Docker Desktop
- ✅ Git

## 🛠️ FFmpeg安装

### Windows安装FFmpeg

**方法1: 使用Chocolatey (推荐)**
```bash
# 安装Chocolatey (如果未安装)
# 以管理员身份运行PowerShell
Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# 安装FFmpeg
choco install ffmpeg

# 验证安装
ffmpeg -version
```

**方法2: 手动安装**
1. 访问 https://ffmpeg.org/download.html#build-windows
2. 下载Windows版本
3. 解压到 `C:\ffmpeg`
4. 添加 `C:\ffmpeg\bin` 到系统PATH环境变量
5. 重启命令行，运行 `ffmpeg -version` 验证

## 🚀 一键启动

### 方式1: 使用启动脚本 (推荐)

```bash
# 双击运行或在命令行执行
start.bat
```

这个脚本会自动：
1. 创建Python虚拟环境
2. 安装依赖
3. 启动数据库服务
4. 运行数据库迁移
5. 启动应用

### 方式2: 手动步骤

```bash
# 1. 进入项目目录
cd d:\code\cut_backend

# 2. 创建虚拟环境
python -m venv venv

# 3. 激活虚拟环境
venv\Scripts\activate

# 4. 安装依赖
pip install -r requirements.txt

# 5. 复制环境配置
copy .env.example .env

# 6. 启动数据库服务
docker-compose -f docker-compose.dev.yml up -d

# 7. 等待数据库启动 (约30秒)
# 检查服务状态
docker-compose -f docker-compose.dev.yml ps

# 8. 运行数据库迁移
alembic upgrade head

# 9. 启动应用
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

## 🔧 环境配置

编辑 `.env` 文件，配置以下内容：

```env
# 数据库配置 (使用Docker中的服务)
DATABASE_URL=postgresql://postgres:password@localhost:5432/video_db
REDIS_URL=redis://localhost:6379
MILVUS_HOST=localhost
MILVUS_PORT=19530

# 项目配置
PROJECT_ROOT_DIR=./projects
SECRET_KEY=your_secret_key_here_change_in_production
DEBUG=true

# AI服务配置 (可选)
OPENAI_API_KEY=your_openai_api_key_here
VOLCANO_APP_ID=your_volcano_app_id
VOLCANO_ACCESS_KEY=your_volcano_access_key
VOLCANO_SECRET_KEY=your_volcano_secret_key
```

## 🌐 访问应用

启动成功后，您可以访问：

- **API文档**: http://localhost:8000/docs
- **ReDoc文档**: http://localhost:8000/redoc
- **健康检查**: http://localhost:8000/health
- **根路径**: http://localhost:8000/

## 📊 数据库管理

### 查看数据库服务状态
```bash
docker-compose -f docker-compose.dev.yml ps
```

### 连接PostgreSQL数据库
```bash
# 使用Docker连接
docker exec -it video_db psql -U postgres -d video_db

# 或使用本地客户端
psql -h localhost -p 5432 -U postgres -d video_db
```

### 查看Redis
```bash
# 连接Redis
docker exec -it video_redis redis-cli

# 查看所有键
KEYS *
```

### 数据库迁移命令
```bash
# 创建新迁移
alembic revision --autogenerate -m "描述"

# 应用迁移
alembic upgrade head

# 回滚迁移
alembic downgrade -1

# 查看迁移历史
alembic history
```

## 🧪 测试API

### 使用curl测试
```bash
# 健康检查
curl http://localhost:8000/health

# 创建项目
curl -X POST "http://localhost:8000/api/v1/projects" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "测试项目",
    "description": "这是一个测试项目",
    "user_id": "123e4567-e89b-12d3-a456-426614174000"
  }'

# 获取项目列表
curl "http://localhost:8000/api/v1/projects?user_id=123e4567-e89b-12d3-a456-426614174000"
```

### 使用Swagger UI测试
访问 http://localhost:8000/docs 使用交互式API文档

## 📁 项目目录结构

启动后会自动创建以下目录：
```
cut_backend/
├── projects/           # 项目文件存储
│   └── {user_id}/
│       └── {project_id}/
│           ├── videos/     # 视频文件
│           ├── audio/      # 音频文件
│           └── thumbnails/ # 缩略图
├── logs/              # 应用日志
└── venv/              # Python虚拟环境
```

## 🛑 停止服务

### 使用停止脚本
```bash
stop.bat
```

### 手动停止
```bash
# 停止应用 (Ctrl+C)

# 停止数据库服务
docker-compose -f docker-compose.dev.yml down

# 停止并删除数据 (谨慎使用)
docker-compose -f docker-compose.dev.yml down -v
```

## 🔍 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   netstat -ano | findstr :8000
   netstat -ano | findstr :5432
   
   # 修改端口 (在docker-compose.dev.yml中)
   ```

2. **数据库连接失败**
   ```bash
   # 检查Docker服务状态
   docker-compose -f docker-compose.dev.yml ps
   
   # 查看数据库日志
   docker-compose -f docker-compose.dev.yml logs db
   ```

3. **虚拟环境问题**
   ```bash
   # 删除并重新创建虚拟环境
   rmdir /s venv
   python -m venv venv
   venv\Scripts\activate
   pip install -r requirements.txt
   ```

4. **FFmpeg未找到**
   ```bash
   # 验证FFmpeg安装
   ffmpeg -version
   
   # 如果未安装，按照上面的FFmpeg安装步骤操作
   ```

### 日志查看
```bash
# 应用日志
tail -f logs/app.log

# Docker服务日志
docker-compose -f docker-compose.dev.yml logs -f
```

## 🎯 下一步

1. **上传测试视频**: 将视频文件放入 `projects/{user_id}/{project_id}/videos/` 目录
2. **扫描视频**: 使用API扫描项目中的视频文件
3. **配置AI服务**: 在 `.env` 中配置OpenAI和火山引擎API密钥
4. **开发前端**: 使用API文档开发前端界面

## 📞 获取帮助

如果遇到问题：
1. 查看 `logs/app.log` 应用日志
2. 检查Docker服务状态
3. 验证环境配置文件
4. 确认所有依赖已正确安装
