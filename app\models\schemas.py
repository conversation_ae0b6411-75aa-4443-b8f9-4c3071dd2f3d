"""
Pydantic模式定义
"""
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from uuid import UUID
from decimal import Decimal

# 项目相关模式
class ProjectBase(BaseModel):
    name: str = Field(..., description="项目名称")
    description: Optional[str] = Field(None, description="项目描述")
    resource_path: str = Field(..., description="项目资源目录路径")
    settings: Optional[Dict[str, Any]] = Field(default_factory=dict, description="项目设置")

class ProjectCreate(ProjectBase):
    user_id: UUID = Field(..., description="用户ID")

class ProjectUpdate(BaseModel):
    name: Optional[str] = Field(None, description="项目名称")
    description: Optional[str] = Field(None, description="项目描述")
    settings: Optional[Dict[str, Any]] = Field(None, description="项目设置")

class ProjectResponse(ProjectBase):
    id: UUID
    user_id: UUID
    status: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

# 视频相关模式
class VideoBase(BaseModel):
    filename: str = Field(..., description="文件名")
    relative_path: str = Field(..., description="相对路径")
    file_size: int = Field(..., description="文件大小")
    duration: Decimal = Field(..., description="视频时长")
    width: Optional[int] = Field(None, description="视频宽度")
    height: Optional[int] = Field(None, description="视频高度")
    fps: Optional[Decimal] = Field(None, description="帧率")
    format: Optional[str] = Field(None, description="视频格式")

class VideoResponse(VideoBase):
    id: UUID
    project_id: UUID
    processing_status: str
    created_at: datetime
    updated_at: datetime
    last_scanned: datetime
    video_metadata: Dict[str, Any]
    
    class Config:
        from_attributes = True

# 场次相关模式
class SceneBase(BaseModel):
    scene_number: int = Field(..., description="场次编号")
    start_time: Decimal = Field(..., description="开始时间")
    end_time: Decimal = Field(..., description="结束时间")
    title: Optional[str] = Field(None, description="场次标题")
    description: Optional[str] = Field(None, description="场次描述")
    location: Optional[str] = Field(None, description="拍摄地点")
    time_of_day: Optional[str] = Field(None, description="时间段")
    characters: Optional[List[str]] = Field(default_factory=list, description="角色列表")
    props: Optional[List[str]] = Field(default_factory=list, description="道具列表")
    assets: Optional[List[str]] = Field(default_factory=list, description="资产列表")
    tags: Optional[List[str]] = Field(default_factory=list, description="标签列表")
    mood: Optional[str] = Field(None, description="情绪氛围")
    action_type: Optional[str] = Field(None, description="动作类型")
    dialogue_summary: Optional[str] = Field(None, description="对话摘要")
    visual_description: Optional[str] = Field(None, description="视觉描述")

class SceneUpdate(BaseModel):
    title: Optional[str] = Field(None, description="场次标题")
    description: Optional[str] = Field(None, description="场次描述")
    location: Optional[str] = Field(None, description="拍摄地点")
    time_of_day: Optional[str] = Field(None, description="时间段")
    characters: Optional[List[str]] = Field(None, description="角色列表")
    props: Optional[List[str]] = Field(None, description="道具列表")
    assets: Optional[List[str]] = Field(None, description="资产列表")
    tags: Optional[List[str]] = Field(None, description="标签列表")
    mood: Optional[str] = Field(None, description="情绪氛围")
    action_type: Optional[str] = Field(None, description="动作类型")
    dialogue_summary: Optional[str] = Field(None, description="对话摘要")
    visual_description: Optional[str] = Field(None, description="视觉描述")

class SceneResponse(SceneBase):
    id: UUID
    video_id: UUID
    ai_generated: bool
    human_verified: bool
    confidence_score: Optional[Decimal]
    created_at: datetime
    updated_at: datetime
    scene_metadata: Dict[str, Any]
    
    class Config:
        from_attributes = True

# 收藏相关模式
class SceneFavoriteCreate(BaseModel):
    scene_id: UUID = Field(..., description="场次ID")
    notes: Optional[str] = Field(None, description="用户备注")
    tags: Optional[List[str]] = Field(default_factory=list, description="用户标签")

class SceneFavoriteUpdate(BaseModel):
    notes: Optional[str] = Field(None, description="用户备注")
    tags: Optional[List[str]] = Field(None, description="用户标签")

class SceneFavoriteResponse(BaseModel):
    id: UUID
    project_id: UUID
    scene_id: UUID
    user_id: UUID
    notes: Optional[str]
    tags: List[str]
    created_at: datetime
    updated_at: datetime
    scene: SceneResponse  # 包含场次信息
    
    class Config:
        from_attributes = True

# 搜索相关模式
class SceneSearchRequest(BaseModel):
    query: str = Field(..., description="搜索查询")
    project_id: Optional[UUID] = Field(None, description="项目ID过滤")
    limit: int = Field(10, ge=1, le=100, description="返回结果数量")
    similarity_threshold: float = Field(0.7, ge=0.0, le=1.0, description="相似度阈值")

class SceneSearchResult(BaseModel):
    scene: SceneResponse
    similarity_score: float = Field(..., description="相似度分数")

class SceneSearchResponse(BaseModel):
    results: List[SceneSearchResult]
    total: int
    query: str
