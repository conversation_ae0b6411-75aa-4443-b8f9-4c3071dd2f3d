"""
搜索服务 - 混合搜索（向量搜索 + 传统搜索 + 重排序）
"""
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from uuid import UUID
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, text
import numpy as np

from app.models.scene import Scene
from app.models.video import Video
from app.models.project import Project
from app.models.scene import Subtitle
from app.services.vector_service import VectorService

class SearchService:
    """混合搜索服务"""
    
    def __init__(self, db: Session):
        self.db = db
        self.vector_service = VectorService(db)
    
    async def hybrid_search_scenes(self, query: str, user_id: UUID, 
                                  project_id: Optional[UUID] = None,
                                  limit: int = 20) -> List[Dict[str, Any]]:
        """混合搜索场次"""
        try:
            # 1. 向量搜索
            vector_results = await self._vector_search_scenes(query, user_id, project_id, limit * 2)
            
            # 2. 传统文本搜索
            text_results = await self._text_search_scenes(query, user_id, project_id, limit * 2)
            
            # 3. 合并和重排序
            merged_results = await self._merge_and_rerank_results(
                vector_results, text_results, query, limit
            )
            
            return merged_results
            
        except Exception as e:
            print(f"混合搜索失败: {e}")
            # 降级到传统搜索
            return await self._text_search_scenes(query, user_id, project_id, limit)
    
    async def _vector_search_scenes(self, query: str, user_id: UUID, 
                                   project_id: Optional[UUID] = None,
                                   limit: int = 40) -> List[Dict[str, Any]]:
        """向量搜索场次"""
        try:
            # 使用向量服务搜索相似场次
            similar_scenes = await self.vector_service.search_similar_scenes(query, limit)
            
            # 获取场次详细信息并验证权限
            results = []
            for item in similar_scenes:
                scene_id = item.get("scene_id")
                if not scene_id:
                    continue
                
                scene = self.db.query(Scene).join(Video).join(Project).filter(
                    and_(
                        Scene.id == scene_id,
                        Project.user_id == user_id,
                        Project.status == 'active'
                    )
                ).first()
                
                if scene:
                    if project_id is None or scene.video.project_id == project_id:
                        results.append({
                            "scene": scene,
                            "score": item.get("similarity_score", 0),
                            "search_type": "vector",
                            "match_type": item.get("embedding_type", "unknown"),
                            "match_content": item.get("text_content", "")
                        })
            
            return results
            
        except Exception as e:
            print(f"向量搜索失败: {e}")
            return []
    
    async def _text_search_scenes(self, query: str, user_id: UUID,
                                 project_id: Optional[UUID] = None,
                                 limit: int = 40) -> List[Dict[str, Any]]:
        """传统文本搜索场次"""
        try:
            base_query = self.db.query(Scene).join(Video).join(Project).filter(
                and_(
                    Project.user_id == user_id,
                    Project.status == 'active'
                )
            )
            
            if project_id:
                base_query = base_query.filter(Project.id == project_id)
            
            # 构建搜索条件
            search_terms = query.split()
            search_conditions = []
            
            for term in search_terms:
                term_conditions = or_(
                    Scene.title.ilike(f"%{term}%"),
                    Scene.description.ilike(f"%{term}%"),
                    Scene.dialogue_summary.ilike(f"%{term}%"),
                    Scene.visual_description.ilike(f"%{term}%"),
                    Scene.location.ilike(f"%{term}%"),
                    Scene.mood.ilike(f"%{term}%"),
                    Scene.action_type.ilike(f"%{term}%")
                )
                search_conditions.append(term_conditions)
            
            # 使用AND连接所有搜索条件
            if search_conditions:
                final_condition = and_(*search_conditions)
                scenes = base_query.filter(final_condition).limit(limit).all()
            else:
                scenes = []
            
            # 计算文本相关性分数
            results = []
            for scene in scenes:
                score = self._calculate_text_relevance_score(scene, query)
                results.append({
                    "scene": scene,
                    "score": score,
                    "search_type": "text",
                    "match_type": "text_match",
                    "match_content": self._get_match_content(scene, query)
                })
            
            # 按分数排序
            results.sort(key=lambda x: x["score"], reverse=True)
            return results
            
        except Exception as e:
            print(f"文本搜索失败: {e}")
            return []
    
    def _calculate_text_relevance_score(self, scene: Scene, query: str) -> float:
        """计算文本相关性分数"""
        score = 0.0
        query_lower = query.lower()
        
        # 标题匹配权重最高
        if scene.title and query_lower in scene.title.lower():
            score += 10.0
        
        # 描述匹配
        if scene.description and query_lower in scene.description.lower():
            score += 5.0
        
        # 对话摘要匹配
        if scene.dialogue_summary and query_lower in scene.dialogue_summary.lower():
            score += 3.0
        
        # 视觉描述匹配
        if scene.visual_description and query_lower in scene.visual_description.lower():
            score += 3.0
        
        # 地点匹配
        if scene.location and query_lower in scene.location.lower():
            score += 2.0
        
        # 情绪匹配
        if scene.mood and query_lower in scene.mood.lower():
            score += 2.0
        
        # 动作类型匹配
        if scene.action_type and query_lower in scene.action_type.lower():
            score += 2.0
        
        # 人物匹配
        if scene.characters:
            for character in scene.characters:
                if query_lower in character.lower():
                    score += 1.5
        
        # 标签匹配
        if scene.tags:
            for tag in scene.tags:
                if query_lower in tag.lower():
                    score += 1.0
        
        return score
    
    def _get_match_content(self, scene: Scene, query: str) -> str:
        """获取匹配内容"""
        query_lower = query.lower()
        
        if scene.title and query_lower in scene.title.lower():
            return f"标题: {scene.title}"
        
        if scene.description and query_lower in scene.description.lower():
            return f"描述: {scene.description[:100]}..."
        
        if scene.dialogue_summary and query_lower in scene.dialogue_summary.lower():
            return f"对话: {scene.dialogue_summary[:100]}..."
        
        if scene.visual_description and query_lower in scene.visual_description.lower():
            return f"视觉: {scene.visual_description[:100]}..."
        
        return scene.title or "无标题"
    
    async def _merge_and_rerank_results(self, vector_results: List[Dict[str, Any]], 
                                       text_results: List[Dict[str, Any]], 
                                       query: str, limit: int) -> List[Dict[str, Any]]:
        """合并和重排序结果"""
        # 使用场次ID去重
        seen_scenes = set()
        merged_results = []
        
        # 先添加向量搜索结果
        for result in vector_results:
            scene_id = result["scene"].id
            if scene_id not in seen_scenes:
                seen_scenes.add(scene_id)
                # 向量搜索结果权重
                result["final_score"] = result["score"] * 0.7
                merged_results.append(result)
        
        # 再添加文本搜索结果
        for result in text_results:
            scene_id = result["scene"].id
            if scene_id not in seen_scenes:
                seen_scenes.add(scene_id)
                # 文本搜索结果权重
                result["final_score"] = result["score"] * 0.3
                merged_results.append(result)
            else:
                # 如果场次已存在，增加其分数
                for existing in merged_results:
                    if existing["scene"].id == scene_id:
                        existing["final_score"] += result["score"] * 0.3
                        existing["search_type"] = "hybrid"
                        break
        
        # 按最终分数排序
        merged_results.sort(key=lambda x: x["final_score"], reverse=True)
        
        return merged_results[:limit]
    
    async def search_subtitles(self, query: str, user_id: UUID,
                              video_id: Optional[UUID] = None,
                              project_id: Optional[UUID] = None,
                              limit: int = 50) -> List[Dict[str, Any]]:
        """搜索字幕"""
        try:
            base_query = self.db.query(Subtitle).join(Video).join(Project).filter(
                and_(
                    Project.user_id == user_id,
                    Project.status == 'active'
                )
            )
            
            if project_id:
                base_query = base_query.filter(Project.id == project_id)
            
            if video_id:
                base_query = base_query.filter(Subtitle.video_id == video_id)
            
            # 文本搜索
            search_filter = or_(
                Subtitle.text.ilike(f"%{query}%"),
                Subtitle.speaker.ilike(f"%{query}%")
            )
            
            subtitles = base_query.filter(search_filter).order_by(
                Subtitle.start_time
            ).limit(limit).all()
            
            # 计算相关性分数
            results = []
            for subtitle in subtitles:
                score = self._calculate_subtitle_relevance_score(subtitle, query)
                results.append({
                    "subtitle": subtitle,
                    "score": score,
                    "match_content": self._get_subtitle_match_content(subtitle, query)
                })
            
            # 按分数排序
            results.sort(key=lambda x: x["score"], reverse=True)
            return results
            
        except Exception as e:
            print(f"字幕搜索失败: {e}")
            return []
    
    def _calculate_subtitle_relevance_score(self, subtitle: Subtitle, query: str) -> float:
        """计算字幕相关性分数"""
        score = 0.0
        query_lower = query.lower()
        
        # 文本匹配
        if subtitle.text and query_lower in subtitle.text.lower():
            # 完全匹配得分更高
            if query_lower == subtitle.text.lower():
                score += 10.0
            else:
                score += 5.0
        
        # 说话人匹配
        if subtitle.speaker and query_lower in subtitle.speaker.lower():
            score += 3.0
        
        # 置信度加权
        if subtitle.confidence:
            score *= (subtitle.confidence / 100.0)
        
        return score
    
    def _get_subtitle_match_content(self, subtitle: Subtitle, query: str) -> str:
        """获取字幕匹配内容"""
        query_lower = query.lower()
        
        if subtitle.text and query_lower in subtitle.text.lower():
            return subtitle.text
        
        if subtitle.speaker and query_lower in subtitle.speaker.lower():
            return f"[{subtitle.speaker}] {subtitle.text}"
        
        return subtitle.text
    
    async def get_search_suggestions(self, query: str, user_id: UUID,
                                    project_id: Optional[UUID] = None,
                                    limit: int = 10) -> List[str]:
        """获取搜索建议"""
        try:
            suggestions = []
            
            # 基于场次标题的建议
            base_query = self.db.query(Scene.title).join(Video).join(Project).filter(
                and_(
                    Project.user_id == user_id,
                    Project.status == 'active',
                    Scene.title.isnot(None)
                )
            )
            
            if project_id:
                base_query = base_query.filter(Project.id == project_id)
            
            titles = base_query.filter(
                Scene.title.ilike(f"%{query}%")
            ).distinct().limit(limit).all()
            
            suggestions.extend([title[0] for title in titles])
            
            # 基于人物的建议
            if len(suggestions) < limit:
                characters_query = self.db.query(Scene.characters).join(Video).join(Project).filter(
                    and_(
                        Project.user_id == user_id,
                        Project.status == 'active',
                        Scene.characters.isnot(None)
                    )
                )
                
                if project_id:
                    characters_query = characters_query.filter(Project.id == project_id)
                
                character_results = characters_query.all()
                
                for result in character_results:
                    if result[0]:  # characters is not None
                        for character in result[0]:
                            if query.lower() in character.lower() and character not in suggestions:
                                suggestions.append(character)
                                if len(suggestions) >= limit:
                                    break
                        if len(suggestions) >= limit:
                            break
            
            return suggestions[:limit]
            
        except Exception as e:
            print(f"获取搜索建议失败: {e}")
            return []
