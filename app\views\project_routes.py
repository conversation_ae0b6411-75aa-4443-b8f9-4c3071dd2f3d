"""
项目管理路由
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List
from uuid import UUID
from app.database.connection import get_db
from app.controllers.project_controller import ProjectController
from app.models.schemas import ProjectCreate, ProjectUpdate, ProjectResponse

router = APIRouter()

def get_project_controller(db: Session = Depends(get_db)) -> ProjectController:
    """获取项目控制器实例"""
    return ProjectController(db)

@router.post("/projects", response_model=ProjectResponse)
async def create_project(
    project_data: ProjectCreate,
    controller: ProjectController = Depends(get_project_controller)
):
    """创建项目"""
    try:
        project = controller.create_project(project_data)
        return ProjectResponse.model_validate(project)
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"创建项目失败: {str(e)}")

@router.get("/projects", response_model=List[ProjectResponse])
async def get_projects(
    user_id: UUID = Query(..., description="用户ID"),
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(100, ge=1, le=100, description="返回数量"),
    controller: ProjectController = Depends(get_project_controller)
):
    """获取项目列表"""
    projects = controller.get_projects(user_id, skip, limit)
    return [ProjectResponse.model_validate(project) for project in projects]

@router.get("/projects/{project_id}", response_model=ProjectResponse)
async def get_project(
    project_id: UUID,
    user_id: UUID = Query(..., description="用户ID"),
    controller: ProjectController = Depends(get_project_controller)
):
    """获取项目详情"""
    project = controller.get_project(project_id, user_id)
    if not project:
        raise HTTPException(status_code=404, detail="项目不存在")
    return ProjectResponse.model_validate(project)

@router.put("/projects/{project_id}", response_model=ProjectResponse)
async def update_project(
    project_id: UUID,
    project_data: ProjectUpdate,
    user_id: UUID = Query(..., description="用户ID"),
    controller: ProjectController = Depends(get_project_controller)
):
    """更新项目"""
    project = controller.update_project(project_id, user_id, project_data)
    if not project:
        raise HTTPException(status_code=404, detail="项目不存在")
    return ProjectResponse.model_validate(project)

@router.delete("/projects/{project_id}")
async def delete_project(
    project_id: UUID,
    user_id: UUID = Query(..., description="用户ID"),
    hard_delete: bool = Query(False, description="是否硬删除"),
    controller: ProjectController = Depends(get_project_controller)
):
    """删除项目"""
    if hard_delete:
        success = controller.hard_delete_project(project_id, user_id)
    else:
        success = controller.delete_project(project_id, user_id)

    if not success:
        raise HTTPException(status_code=404, detail="项目不存在")

    return {"message": "项目删除成功"}

@router.get("/projects/{project_id}/stats")
async def get_project_stats(
    project_id: UUID,
    user_id: UUID = Query(..., description="用户ID"),
    controller: ProjectController = Depends(get_project_controller)
):
    """获取项目统计信息"""
    stats = controller.get_project_stats(project_id, user_id)
    if not stats:
        raise HTTPException(status_code=404, detail="项目不存在")
    return stats
