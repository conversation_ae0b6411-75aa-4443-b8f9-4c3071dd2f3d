"""
字幕管理路由
"""
from fastapi import APIRouter, Depends, HTTPException, Query, Response
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID

from app.database.connection import get_db
from app.controllers.subtitle_controller import SubtitleController
from app.models.schemas import SubtitleCreate, SubtitleUpdate, SubtitleResponse

router = APIRouter()

def get_subtitle_controller(db: Session = Depends(get_db)) -> SubtitleController:
    """获取字幕控制器实例"""
    return SubtitleController(db)

@router.post("/subtitles", response_model=SubtitleResponse)
async def create_subtitle(
    subtitle_data: SubtitleCreate,
    user_id: UUID = Query(..., description="用户ID"),
    controller: SubtitleController = Depends(get_subtitle_controller)
):
    """创建字幕"""
    try:
        subtitle = controller.create_subtitle(subtitle_data, user_id)
        return SubtitleResponse.model_validate(subtitle)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建字幕失败: {str(e)}")

@router.get("/subtitles/{subtitle_id}", response_model=SubtitleResponse)
async def get_subtitle(
    subtitle_id: UUID,
    user_id: UUID = Query(..., description="用户ID"),
    controller: SubtitleController = Depends(get_subtitle_controller)
):
    """获取字幕详情"""
    subtitle = controller.get_subtitle(subtitle_id, user_id)
    if not subtitle:
        raise HTTPException(status_code=404, detail="字幕不存在")
    return SubtitleResponse.model_validate(subtitle)

@router.put("/subtitles/{subtitle_id}", response_model=SubtitleResponse)
async def update_subtitle(
    subtitle_id: UUID,
    subtitle_data: SubtitleUpdate,
    user_id: UUID = Query(..., description="用户ID"),
    controller: SubtitleController = Depends(get_subtitle_controller)
):
    """更新字幕"""
    subtitle = controller.update_subtitle(subtitle_id, subtitle_data, user_id)
    if not subtitle:
        raise HTTPException(status_code=404, detail="字幕不存在")
    return SubtitleResponse.model_validate(subtitle)

@router.delete("/subtitles/{subtitle_id}")
async def delete_subtitle(
    subtitle_id: UUID,
    user_id: UUID = Query(..., description="用户ID"),
    controller: SubtitleController = Depends(get_subtitle_controller)
):
    """删除字幕"""
    success = controller.delete_subtitle(subtitle_id, user_id)
    if not success:
        raise HTTPException(status_code=404, detail="字幕不存在")
    return {"message": "字幕删除成功"}

@router.get("/videos/{video_id}/subtitles", response_model=List[SubtitleResponse])
async def get_video_subtitles(
    video_id: UUID,
    user_id: UUID = Query(..., description="用户ID"),
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(1000, ge=1, le=1000, description="返回数量"),
    controller: SubtitleController = Depends(get_subtitle_controller)
):
    """获取视频的所有字幕"""
    subtitles = controller.get_video_subtitles(video_id, user_id, skip, limit)
    return [SubtitleResponse.model_validate(subtitle) for subtitle in subtitles]

@router.get("/videos/{video_id}/subtitles/time-range", response_model=List[SubtitleResponse])
async def get_subtitles_by_time_range(
    video_id: UUID,
    start_time: float = Query(..., description="开始时间（秒）"),
    end_time: float = Query(..., description="结束时间（秒）"),
    user_id: UUID = Query(..., description="用户ID"),
    controller: SubtitleController = Depends(get_subtitle_controller)
):
    """根据时间范围获取字幕"""
    subtitles = controller.get_subtitles_by_time_range(video_id, start_time, end_time, user_id)
    return [SubtitleResponse.model_validate(subtitle) for subtitle in subtitles]

@router.get("/subtitles/search", response_model=List[SubtitleResponse])
async def search_subtitles(
    q: str = Query(..., description="搜索关键词"),
    video_id: Optional[UUID] = Query(None, description="视频ID"),
    user_id: UUID = Query(..., description="用户ID"),
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(100, ge=1, le=100, description="返回数量"),
    controller: SubtitleController = Depends(get_subtitle_controller)
):
    """搜索字幕"""
    subtitles = controller.search_subtitles(q, video_id, user_id, skip, limit)
    return [SubtitleResponse.model_validate(subtitle) for subtitle in subtitles]

@router.get("/subtitles/by-speaker", response_model=List[SubtitleResponse])
async def get_subtitles_by_speaker(
    speaker: str = Query(..., description="说话人"),
    video_id: Optional[UUID] = Query(None, description="视频ID"),
    user_id: UUID = Query(..., description="用户ID"),
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(100, ge=1, le=100, description="返回数量"),
    controller: SubtitleController = Depends(get_subtitle_controller)
):
    """根据说话人获取字幕"""
    subtitles = controller.get_subtitles_by_speaker(speaker, video_id, user_id, skip, limit)
    return [SubtitleResponse.model_validate(subtitle) for subtitle in subtitles]

@router.get("/videos/{video_id}/subtitles/export/srt")
async def export_subtitles_srt(
    video_id: UUID,
    user_id: UUID = Query(..., description="用户ID"),
    controller: SubtitleController = Depends(get_subtitle_controller)
):
    """导出SRT格式字幕"""
    try:
        srt_content = controller.export_subtitles_srt(video_id, user_id)
        if not srt_content:
            raise HTTPException(status_code=404, detail="视频不存在或无字幕数据")
        
        return Response(
            content=srt_content,
            media_type="text/plain",
            headers={"Content-Disposition": f"attachment; filename=subtitles_{video_id}.srt"}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")

@router.get("/videos/{video_id}/subtitles/export/vtt")
async def export_subtitles_vtt(
    video_id: UUID,
    user_id: UUID = Query(..., description="用户ID"),
    controller: SubtitleController = Depends(get_subtitle_controller)
):
    """导出VTT格式字幕"""
    try:
        vtt_content = controller.export_subtitles_vtt(video_id, user_id)
        if not vtt_content or vtt_content == "WEBVTT\n\n":
            raise HTTPException(status_code=404, detail="视频不存在或无字幕数据")
        
        return Response(
            content=vtt_content,
            media_type="text/vtt",
            headers={"Content-Disposition": f"attachment; filename=subtitles_{video_id}.vtt"}
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")

@router.get("/videos/{video_id}/subtitles/stats")
async def get_subtitle_stats(
    video_id: UUID,
    user_id: UUID = Query(..., description="用户ID"),
    controller: SubtitleController = Depends(get_subtitle_controller)
):
    """获取视频字幕统计"""
    stats = controller.get_subtitle_stats(video_id, user_id)
    if not stats:
        raise HTTPException(status_code=404, detail="视频不存在或无字幕数据")
    return stats
