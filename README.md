# 视频场次分割后端系统

智能视频场次分割后端系统，支持项目资源目录管理、语音识别、AI场次分割、向量化搜索和场次收藏等功能。

## 功能特性

- 🚀 现代化Python项目管理 (uv)
- 📁 项目资源目录自动管理
- 🤖 AI智能场次分割
- ⭐ 场次收藏与标签管理
- 🔍 向量化智能搜索
- 📊 完整的监控体系

## 技术栈

- **后端框架**: FastAPI
- **数据库**: PostgreSQL + Milvus
- **消息队列**: Redis + Celery
- **AI服务**: 火山引擎语音识别 + OpenAI/Claude

## 快速开始

### 环境要求

- Python 3.11+
- PostgreSQL 15+
- Redis 6.2+
- Milvus 2.3+

### 安装依赖

```bash
# 使用uv安装依赖
uv sync

# 或使用pip
pip install -r requirements.txt
```

### 配置环境

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件
# 配置数据库连接、AI服务密钥等
```

### 运行应用

```bash
# 开发模式
uvicorn app.main:app --reload

# 或使用uv
uv run uvicorn app.main:app --reload
```

### API文档

启动应用后访问：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 项目结构

```
backend/
├── app/
│   ├── models/              # 数据模型层
│   ├── controllers/         # 控制器层
│   ├── views/              # 视图层 (API路由)
│   ├── services/           # 业务服务层
│   ├── database/           # 数据库配置
│   └── config/             # 配置文件
├── projects/               # 项目资源目录
├── tests/                  # 测试文件
├── pyproject.toml          # 项目配置
└── README.md
```

## 开发指南

### 代码规范

```bash
# 代码格式化
black app/
isort app/

# 类型检查
mypy app/

# 代码检查
flake8 app/
```

### 测试

```bash
# 运行测试
pytest

# 测试覆盖率
pytest --cov=app
```

## 部署

### Docker部署

```bash
# 构建镜像
docker build -t video-scene-backend .

# 运行容器
docker-compose up -d
```

## 许可证

MIT License
