"""
场次收藏管理控制器
"""
from typing import List, Optional, Dict, Any
from uuid import UUID
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc

from app.models.favorite import SceneFavorite
from app.models.scene import Scene
from app.models.video import Video
from app.models.project import Project
from app.models.schemas import SceneFavoriteCreate, SceneFavoriteUpdate

class SceneFavoriteController:
    """场次收藏管理控制器"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def add_favorite(self, favorite_data: SceneFavoriteCreate, user_id: UUID) -> SceneFavorite:
        """添加场次收藏"""
        # 验证场次权限
        scene = self.db.query(Scene).join(Video).join(Project).filter(
            and_(
                Scene.id == favorite_data.scene_id,
                Project.user_id == user_id,
                Project.status == 'active'
            )
        ).first()
        
        if not scene:
            raise ValueError("场次不存在或无权限访问")
        
        # 检查是否已收藏
        existing = self.db.query(SceneFavorite).filter(
            and_(
                SceneFavorite.scene_id == favorite_data.scene_id,
                SceneFavorite.user_id == user_id
            )
        ).first()
        
        if existing:
            raise ValueError("场次已收藏")
        
        # 创建收藏
        favorite = SceneFavorite(
            scene_id=favorite_data.scene_id,
            user_id=user_id,
            notes=favorite_data.notes,
            tags=favorite_data.tags or [],
            rating=favorite_data.rating
        )
        
        self.db.add(favorite)
        self.db.commit()
        self.db.refresh(favorite)
        
        return favorite
    
    def get_favorite(self, favorite_id: UUID, user_id: UUID) -> Optional[SceneFavorite]:
        """获取收藏详情"""
        return self.db.query(SceneFavorite).filter(
            and_(
                SceneFavorite.id == favorite_id,
                SceneFavorite.user_id == user_id
            )
        ).first()
    
    def get_scene_favorite(self, scene_id: UUID, user_id: UUID) -> Optional[SceneFavorite]:
        """获取场次的收藏记录"""
        return self.db.query(SceneFavorite).filter(
            and_(
                SceneFavorite.scene_id == scene_id,
                SceneFavorite.user_id == user_id
            )
        ).first()
    
    def get_user_favorites(self, user_id: UUID, project_id: Optional[UUID] = None,
                          skip: int = 0, limit: int = 100) -> List[SceneFavorite]:
        """获取用户的收藏列表"""
        query = self.db.query(SceneFavorite).join(Scene).join(Video).join(Project).filter(
            and_(
                SceneFavorite.user_id == user_id,
                Project.status == 'active'
            )
        )
        
        if project_id:
            query = query.filter(Project.id == project_id)
        
        return query.order_by(desc(SceneFavorite.created_at)).offset(skip).limit(limit).all()
    
    def update_favorite(self, favorite_id: UUID, favorite_data: SceneFavoriteUpdate, 
                       user_id: UUID) -> Optional[SceneFavorite]:
        """更新收藏"""
        favorite = self.get_favorite(favorite_id, user_id)
        if not favorite:
            return None
        
        # 更新字段
        update_data = favorite_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(favorite, field):
                setattr(favorite, field, value)
        
        self.db.commit()
        self.db.refresh(favorite)
        
        return favorite
    
    def remove_favorite(self, favorite_id: UUID, user_id: UUID) -> bool:
        """移除收藏"""
        favorite = self.get_favorite(favorite_id, user_id)
        if not favorite:
            return False
        
        self.db.delete(favorite)
        self.db.commit()
        return True
    
    def remove_scene_favorite(self, scene_id: UUID, user_id: UUID) -> bool:
        """移除场次收藏"""
        favorite = self.get_scene_favorite(scene_id, user_id)
        if not favorite:
            return False
        
        self.db.delete(favorite)
        self.db.commit()
        return True
    
    def search_favorites(self, query: str, user_id: UUID, project_id: Optional[UUID] = None,
                        skip: int = 0, limit: int = 50) -> List[SceneFavorite]:
        """搜索收藏"""
        base_query = self.db.query(SceneFavorite).join(Scene).join(Video).join(Project).filter(
            and_(
                SceneFavorite.user_id == user_id,
                Project.status == 'active'
            )
        )
        
        if project_id:
            base_query = base_query.filter(Project.id == project_id)
        
        # 搜索收藏备注、场次标题、场次描述
        from sqlalchemy import or_
        search_filter = or_(
            SceneFavorite.notes.ilike(f"%{query}%"),
            Scene.title.ilike(f"%{query}%"),
            Scene.description.ilike(f"%{query}%")
        )
        
        return base_query.filter(search_filter).order_by(
            desc(SceneFavorite.created_at)
        ).offset(skip).limit(limit).all()
    
    def get_favorites_by_tags(self, tags: List[str], user_id: UUID, 
                             project_id: Optional[UUID] = None,
                             skip: int = 0, limit: int = 50) -> List[SceneFavorite]:
        """根据标签获取收藏"""
        base_query = self.db.query(SceneFavorite).join(Scene).join(Video).join(Project).filter(
            and_(
                SceneFavorite.user_id == user_id,
                Project.status == 'active'
            )
        )
        
        if project_id:
            base_query = base_query.filter(Project.id == project_id)
        
        # 搜索包含指定标签的收藏
        for tag in tags:
            base_query = base_query.filter(SceneFavorite.tags.contains([tag]))
        
        return base_query.order_by(desc(SceneFavorite.created_at)).offset(skip).limit(limit).all()
    
    def get_favorites_by_rating(self, min_rating: int, user_id: UUID,
                               project_id: Optional[UUID] = None,
                               skip: int = 0, limit: int = 50) -> List[SceneFavorite]:
        """根据评分获取收藏"""
        base_query = self.db.query(SceneFavorite).join(Scene).join(Video).join(Project).filter(
            and_(
                SceneFavorite.user_id == user_id,
                Project.status == 'active',
                SceneFavorite.rating >= min_rating
            )
        )
        
        if project_id:
            base_query = base_query.filter(Project.id == project_id)
        
        return base_query.order_by(desc(SceneFavorite.rating), desc(SceneFavorite.created_at)).offset(skip).limit(limit).all()
    
    def get_project_favorites(self, project_id: UUID, user_id: UUID,
                             skip: int = 0, limit: int = 100) -> List[SceneFavorite]:
        """获取项目收藏列表"""
        return self.db.query(SceneFavorite).join(Scene).join(Video).join(Project).filter(
            and_(
                Project.id == project_id,
                SceneFavorite.user_id == user_id,
                Project.status == 'active'
            )
        ).order_by(desc(SceneFavorite.created_at)).offset(skip).limit(limit).all()
    
    def is_scene_favorited(self, scene_id: UUID, user_id: UUID) -> bool:
        """检查场次是否已收藏"""
        favorite = self.get_scene_favorite(scene_id, user_id)
        return favorite is not None
    
    def get_favorite_stats(self, user_id: UUID, project_id: Optional[UUID] = None) -> Dict[str, Any]:
        """获取收藏统计"""
        favorites = self.get_user_favorites(user_id, project_id, limit=10000)
        
        if not favorites:
            return {}
        
        # 统计信息
        total_favorites = len(favorites)
        
        # 统计评分
        ratings = [f.rating for f in favorites if f.rating is not None]
        avg_rating = sum(ratings) / len(ratings) if ratings else 0
        
        rating_distribution = {}
        for rating in ratings:
            rating_distribution[rating] = rating_distribution.get(rating, 0) + 1
        
        # 统计标签
        all_tags = []
        for favorite in favorites:
            if favorite.tags:
                all_tags.extend(favorite.tags)
        
        tag_counts = {}
        for tag in all_tags:
            tag_counts[tag] = tag_counts.get(tag, 0) + 1
        
        # 统计项目分布
        project_counts = {}
        for favorite in favorites:
            scene = favorite.scene
            if scene and scene.video and scene.video.project:
                project_name = scene.video.project.name
                project_counts[project_name] = project_counts.get(project_name, 0) + 1
        
        return {
            "user_id": str(user_id),
            "project_id": str(project_id) if project_id else None,
            "total_favorites": total_favorites,
            "average_rating": avg_rating,
            "rating_distribution": rating_distribution,
            "top_tags": sorted(tag_counts.items(), key=lambda x: x[1], reverse=True)[:10],
            "project_distribution": project_counts
        }
    
    def bulk_add_favorites(self, scene_ids: List[UUID], user_id: UUID, 
                          notes: Optional[str] = None, tags: Optional[List[str]] = None) -> Dict[str, Any]:
        """批量添加收藏"""
        results = {
            "added": [],
            "skipped": [],
            "errors": []
        }
        
        for scene_id in scene_ids:
            try:
                favorite_data = SceneFavoriteCreate(
                    scene_id=scene_id,
                    notes=notes,
                    tags=tags or []
                )
                favorite = self.add_favorite(favorite_data, user_id)
                results["added"].append(str(scene_id))
            except ValueError as e:
                if "已收藏" in str(e):
                    results["skipped"].append(str(scene_id))
                else:
                    results["errors"].append({"scene_id": str(scene_id), "error": str(e)})
            except Exception as e:
                results["errors"].append({"scene_id": str(scene_id), "error": str(e)})
        
        return results
    
    def bulk_remove_favorites(self, scene_ids: List[UUID], user_id: UUID) -> Dict[str, Any]:
        """批量移除收藏"""
        results = {
            "removed": [],
            "not_found": [],
            "errors": []
        }
        
        for scene_id in scene_ids:
            try:
                success = self.remove_scene_favorite(scene_id, user_id)
                if success:
                    results["removed"].append(str(scene_id))
                else:
                    results["not_found"].append(str(scene_id))
            except Exception as e:
                results["errors"].append({"scene_id": str(scene_id), "error": str(e)})
        
        return results
