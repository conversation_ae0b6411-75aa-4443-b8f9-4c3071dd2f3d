# 部署指南

## 环境要求

### 系统要求
- Python 3.11+
- PostgreSQL 15+
- Redis 6.2+
- FFmpeg (用于视频处理)

### 可选组件
- Milvus 2.3+ (向量搜索)
- Docker & Docker Compose (容器化部署)

## 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd cut_backend
```

### 2. 安装依赖

#### 使用uv (推荐)
```bash
# 安装uv
pip install uv

# 安装项目依赖
uv sync
```

#### 使用pip
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 3. 配置环境变量
```bash
# 复制环境配置文件
cp .env.example .env

# 编辑.env文件，配置以下内容：
# - 数据库连接信息
# - Redis连接信息
# - AI服务API密钥
# - 项目根目录路径
```

### 4. 数据库设置

#### PostgreSQL安装和配置
```bash
# 创建数据库
createdb video_db

# 或使用SQL
psql -c "CREATE DATABASE video_db;"
```

#### 运行数据库迁移
```bash
# 初始化Alembic
alembic init app/database/migrations

# 创建初始迁移
alembic revision --autogenerate -m "Initial migration"

# 应用迁移
alembic upgrade head
```

### 5. 启动应用

#### 开发模式
```bash
# 使用uv
uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 或使用uvicorn
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### 生产模式
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
```

### 6. 验证安装
访问以下URL验证安装：
- API文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/health
- 根路径: http://localhost:8000/

## Docker部署

### 1. 创建Dockerfile
```dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    ffmpeg \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建项目目录
RUN mkdir -p /app/projects

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 2. 创建docker-compose.yml
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/video_db
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis
    volumes:
      - ./projects:/app/projects

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=video_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  milvus:
    image: milvusdb/milvus:v2.3.0
    ports:
      - "19530:19530"
    environment:
      - ETCD_ENDPOINTS=etcd:2379
      - MINIO_ADDRESS=minio:9000
    depends_on:
      - etcd
      - minio

volumes:
  postgres_data:
```

### 3. 启动服务
```bash
docker-compose up -d
```

## 配置说明

### 环境变量
| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| DATABASE_URL | PostgreSQL连接URL | postgresql://user:password@localhost:5432/video_db |
| REDIS_URL | Redis连接URL | redis://localhost:6379 |
| MILVUS_HOST | Milvus主机地址 | localhost |
| MILVUS_PORT | Milvus端口 | 19530 |
| OPENAI_API_KEY | OpenAI API密钥 | - |
| VOLCANO_APP_ID | 火山引擎应用ID | - |
| VOLCANO_ACCESS_KEY | 火山引擎访问密钥 | - |
| VOLCANO_SECRET_KEY | 火山引擎密钥 | - |
| PROJECT_ROOT_DIR | 项目文件根目录 | ./projects |
| SECRET_KEY | JWT密钥 | your_secret_key_here |

### 项目目录结构
```
projects/
├── {user_id}/
│   └── {project_id}/
│       ├── videos/          # 视频文件
│       ├── audio/           # 提取的音频文件
│       └── thumbnails/      # 视频缩略图
```

## 监控和日志

### 日志配置
应用使用Python标准logging模块，日志级别通过LOG_LEVEL环境变量控制。

### 健康检查
- `/health` - 基础健康检查
- `/api/v1/projects` - 数据库连接检查

### 性能监控
建议使用以下工具进行监控：
- Prometheus + Grafana (指标监控)
- ELK Stack (日志分析)
- Sentry (错误追踪)

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查PostgreSQL服务是否运行
   - 验证DATABASE_URL配置
   - 确认数据库用户权限

2. **视频处理失败**
   - 确认FFmpeg已安装
   - 检查视频文件格式是否支持
   - 验证文件路径权限

3. **语音识别失败**
   - 检查火山引擎API配置
   - 验证网络连接
   - 确认API配额

4. **向量搜索不可用**
   - 检查Milvus服务状态
   - 验证连接配置
   - 确认向量数据是否已创建

### 性能优化

1. **数据库优化**
   - 创建适当的索引
   - 配置连接池
   - 定期清理过期数据

2. **文件存储优化**
   - 使用对象存储(如S3)
   - 配置CDN加速
   - 实现文件压缩

3. **缓存优化**
   - Redis缓存热点数据
   - 实现查询结果缓存
   - 配置适当的过期时间

## 安全建议

1. **API安全**
   - 实现JWT认证
   - 配置CORS策略
   - 添加请求限流

2. **数据安全**
   - 数据库连接加密
   - 敏感信息加密存储
   - 定期备份数据

3. **文件安全**
   - 文件类型验证
   - 文件大小限制
   - 病毒扫描

## 扩展部署

### 负载均衡
```nginx
upstream backend {
    server app1:8000;
    server app2:8000;
    server app3:8000;
}

server {
    listen 80;
    location / {
        proxy_pass http://backend;
    }
}
```

### 集群部署
- 使用Kubernetes进行容器编排
- 配置水平扩展策略
- 实现服务发现和负载均衡
