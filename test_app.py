"""
简单的应用测试脚本
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试模块导入"""
    try:
        from app.config.settings import settings
        print("✓ 配置模块导入成功")
        
        from app.models import Project, Video, Scene, Subtitle
        print("✓ 数据模型导入成功")
        
        from app.controllers.project_controller import ProjectController
        print("✓ 项目控制器导入成功")
        
        from app.controllers.video_controller import VideoController
        print("✓ 视频控制器导入成功")
        
        from app.services.video_scanner import VideoScanner
        print("✓ 视频扫描服务导入成功")
        
        from app.services.speech_service import SpeechRecognitionService
        print("✓ 语音识别服务导入成功")
        
        return True
    except Exception as e:
        print(f"✗ 模块导入失败: {e}")
        return False

def test_fastapi_app():
    """测试FastAPI应用"""
    try:
        from app.main import app
        print("✓ FastAPI应用创建成功")
        
        # 检查路由
        routes = [route.path for route in app.routes]
        expected_routes = ["/", "/health", "/api/v1/projects"]
        
        for route in expected_routes:
            if any(route in r for r in routes):
                print(f"✓ 路由 {route} 注册成功")
            else:
                print(f"✗ 路由 {route} 未找到")
        
        return True
    except Exception as e:
        print(f"✗ FastAPI应用测试失败: {e}")
        return False

def test_settings():
    """测试配置"""
    try:
        from app.config.settings import settings
        
        print(f"✓ 应用名称: {settings.app_name}")
        print(f"✓ 项目根目录: {settings.project_root_dir}")
        print(f"✓ 数据库URL: {settings.database_url}")
        
        # 检查项目目录是否存在
        if os.path.exists(settings.project_root_dir):
            print(f"✓ 项目根目录已创建: {settings.project_root_dir}")
        else:
            print(f"✗ 项目根目录不存在: {settings.project_root_dir}")
        
        return True
    except Exception as e:
        print(f"✗ 配置测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=== 视频场次分割后端系统测试 ===\n")
    
    tests = [
        ("模块导入测试", test_imports),
        ("配置测试", test_settings),
        ("FastAPI应用测试", test_fastapi_app),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed += 1
            print(f"✓ {test_name} 通过")
        else:
            print(f"✗ {test_name} 失败")
    
    print(f"\n=== 测试结果: {passed}/{total} 通过 ===")
    
    if passed == total:
        print("🎉 所有测试通过！系统基础架构搭建成功。")
        print("\n下一步:")
        print("1. 安装PostgreSQL数据库")
        print("2. 配置.env文件中的数据库连接")
        print("3. 运行数据库迁移")
        print("4. 启动应用: uvicorn app.main:app --reload")
    else:
        print("❌ 部分测试失败，请检查错误信息。")

if __name__ == "__main__":
    main()
