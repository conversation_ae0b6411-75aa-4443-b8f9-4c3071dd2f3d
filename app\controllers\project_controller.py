"""
项目管理控制器
"""
from typing import List, Optional
from uuid import UUID
from sqlalchemy.orm import Session
from sqlalchemy import and_
from app.models.project import Project
from app.models.schemas import ProjectCreate, ProjectUpdate, ProjectResponse
from app.config.settings import settings
import os
import shutil

class ProjectController:
    """项目管理控制器"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_project(self, project_data: ProjectCreate) -> Project:
        """创建项目"""
        # 创建项目资源目录
        project_path = os.path.join(settings.project_root_dir, str(project_data.user_id))
        os.makedirs(project_path, exist_ok=True)
        
        # 创建项目实例
        db_project = Project(
            name=project_data.name,
            description=project_data.description,
            user_id=project_data.user_id,
            resource_path=project_path,
            settings=project_data.settings
        )
        
        self.db.add(db_project)
        self.db.commit()
        self.db.refresh(db_project)
        
        # 创建项目专属目录
        project_specific_path = os.path.join(project_path, str(db_project.id))
        os.makedirs(project_specific_path, exist_ok=True)
        os.makedirs(os.path.join(project_specific_path, "videos"), exist_ok=True)
        
        # 更新项目的资源路径
        db_project.resource_path = project_specific_path
        self.db.commit()
        
        return db_project
    
    def get_projects(self, user_id: UUID, skip: int = 0, limit: int = 100) -> List[Project]:
        """获取用户项目列表"""
        return self.db.query(Project).filter(
            and_(Project.user_id == user_id, Project.status == 'active')
        ).offset(skip).limit(limit).all()
    
    def get_project(self, project_id: UUID, user_id: UUID) -> Optional[Project]:
        """获取项目详情"""
        return self.db.query(Project).filter(
            and_(
                Project.id == project_id,
                Project.user_id == user_id,
                Project.status == 'active'
            )
        ).first()
    
    def update_project(self, project_id: UUID, user_id: UUID, project_data: ProjectUpdate) -> Optional[Project]:
        """更新项目"""
        db_project = self.get_project(project_id, user_id)
        if not db_project:
            return None
        
        # 更新字段
        update_data = project_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_project, field, value)
        
        self.db.commit()
        self.db.refresh(db_project)
        return db_project
    
    def delete_project(self, project_id: UUID, user_id: UUID) -> bool:
        """删除项目（软删除）"""
        db_project = self.get_project(project_id, user_id)
        if not db_project:
            return False
        
        # 软删除：标记为已删除状态
        db_project.status = 'deleted'
        self.db.commit()
        
        return True
    
    def hard_delete_project(self, project_id: UUID, user_id: UUID) -> bool:
        """硬删除项目（包括文件）"""
        db_project = self.get_project(project_id, user_id)
        if not db_project:
            return False
        
        # 删除项目目录
        if os.path.exists(db_project.resource_path):
            try:
                shutil.rmtree(db_project.resource_path)
            except Exception as e:
                print(f"删除项目目录失败: {e}")
        
        # 从数据库删除
        self.db.delete(db_project)
        self.db.commit()
        
        return True
    
    def get_project_stats(self, project_id: UUID, user_id: UUID) -> Optional[dict]:
        """获取项目统计信息"""
        db_project = self.get_project(project_id, user_id)
        if not db_project:
            return None
        
        # 这里可以添加统计逻辑，比如视频数量、场次数量等
        # 暂时返回基础信息
        return {
            "project_id": project_id,
            "video_count": 0,  # 待实现
            "scene_count": 0,  # 待实现
            "favorite_count": 0,  # 待实现
            "total_duration": 0,  # 待实现
        }
