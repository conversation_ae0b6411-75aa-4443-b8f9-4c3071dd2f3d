@echo off
echo ========================================
echo 视频场次分割系统启动脚本
echo ========================================

echo.
echo 1. 检查虚拟环境...
if not exist "venv\Scripts\activate.bat" (
    echo 虚拟环境不存在，正在创建...
    python -m venv venv
    echo 虚拟环境创建完成
)

echo.
echo 2. 激活虚拟环境...
call venv\Scripts\activate.bat

echo.
echo 3. 安装/更新依赖...
pip install -r requirements.txt

echo.
echo 4. 检查环境配置文件...
if not exist ".env" (
    echo 复制环境配置文件...
    copy .env.example .env
    echo.
    echo ⚠️  请编辑 .env 文件配置数据库连接等信息
    echo.
    pause
)

echo.
echo 5. 启动数据库服务...
docker-compose -f docker-compose.dev.yml up -d

echo.
echo 6. 等待数据库启动...
timeout /t 10 /nobreak

echo.
echo 7. 运行数据库迁移...
alembic upgrade head

echo.
echo 8. 创建项目目录...
if not exist "projects" mkdir projects
if not exist "logs" mkdir logs

echo.
echo 9. 启动应用...
echo 应用将在 http://localhost:8000 启动
echo API文档: http://localhost:8000/docs
echo.
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
