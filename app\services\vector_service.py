"""
向量化服务 - 处理文本向量化和Milvus存储
"""
import uuid
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session
import numpy as np
from pymilvus import connections, Collection, FieldSchema, CollectionSchema, DataType, utility
import openai
from sentence_transformers import SentenceTransformer

from app.models.scene import Scene
from app.models.favorite import VectorEmbedding
from app.config.settings import get_settings

settings = get_settings()

class VectorService:
    """向量化服务"""
    
    def __init__(self, db: Session):
        self.db = db
        self.collection_name = "scene_embeddings"
        self.dimension = 768  # 使用sentence-transformers的默认维度
        
        # 初始化嵌入模型
        try:
            # 优先使用OpenAI
            if hasattr(settings, 'OPENAI_API_KEY') and settings.OPENAI_API_KEY:
                openai.api_key = settings.OPENAI_API_KEY
                self.embedding_model = "openai"
            else:
                # 使用本地sentence-transformers模型
                self.embedding_model = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')
                print("Using local sentence-transformers model")
        except Exception as e:
            print(f"Warning: Failed to initialize embedding model: {e}")
            self.embedding_model = None
        
        # 连接Milvus
        self._connect_milvus()
        self._ensure_collection_exists()
    
    def _connect_milvus(self):
        """连接Milvus数据库"""
        try:
            connections.connect(
                alias="default",
                host=settings.MILVUS_HOST,
                port=settings.MILVUS_PORT
            )
            print("Connected to Milvus successfully")
        except Exception as e:
            print(f"Failed to connect to Milvus: {e}")
    
    def _ensure_collection_exists(self):
        """确保集合存在"""
        try:
            if not utility.has_collection(self.collection_name):
                # 定义字段
                fields = [
                    FieldSchema(name="id", dtype=DataType.VARCHAR, max_length=36, is_primary=True),
                    FieldSchema(name="scene_id", dtype=DataType.VARCHAR, max_length=36),
                    FieldSchema(name="embedding_type", dtype=DataType.VARCHAR, max_length=50),
                    FieldSchema(name="text_content", dtype=DataType.VARCHAR, max_length=65535),
                    FieldSchema(name="vector", dtype=DataType.FLOAT_VECTOR, dim=self.dimension)
                ]
                
                # 创建集合
                schema = CollectionSchema(fields, description="Scene embeddings collection")
                collection = Collection(self.collection_name, schema)
                
                # 创建索引
                index_params = {
                    "metric_type": "COSINE",
                    "index_type": "IVF_FLAT",
                    "params": {"nlist": 128}
                }
                collection.create_index("vector", index_params)
                print(f"Created Milvus collection: {self.collection_name}")
            
            self.collection = Collection(self.collection_name)
            self.collection.load()
            
        except Exception as e:
            print(f"Failed to setup Milvus collection: {e}")
            self.collection = None
    
    async def process_scene_embeddings(self, scene_id: str) -> Dict[str, Any]:
        """处理场次的向量化"""
        try:
            # 获取场次信息
            scene = self.db.query(Scene).filter(Scene.id == scene_id).first()
            if not scene:
                raise Exception(f"场次不存在: {scene_id}")
            
            results = []
            
            # 1. 处理对话摘要向量化
            if scene.dialogue_summary:
                dialogue_result = await self._create_embedding(
                    scene_id=scene_id,
                    text_content=scene.dialogue_summary,
                    embedding_type="dialogue"
                )
                results.append(dialogue_result)
            
            # 2. 处理视觉描述向量化
            if scene.visual_description:
                visual_result = await self._create_embedding(
                    scene_id=scene_id,
                    text_content=scene.visual_description,
                    embedding_type="visual"
                )
                results.append(visual_result)
            
            # 3. 处理场次描述向量化
            if scene.description:
                desc_result = await self._create_embedding(
                    scene_id=scene_id,
                    text_content=scene.description,
                    embedding_type="description"
                )
                results.append(desc_result)
            
            # 4. 处理标签和元数据向量化
            metadata_text = self._build_metadata_text(scene)
            if metadata_text:
                metadata_result = await self._create_embedding(
                    scene_id=scene_id,
                    text_content=metadata_text,
                    embedding_type="metadata"
                )
                results.append(metadata_result)
            
            return {
                "status": "success",
                "embeddings_created": len(results),
                "results": results
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    async def _create_embedding(self, scene_id: str, text_content: str, embedding_type: str) -> Dict[str, Any]:
        """创建单个向量嵌入"""
        try:
            # 生成向量
            vector = await self._generate_vector(text_content)
            
            # 生成唯一ID
            embedding_id = str(uuid.uuid4())
            
            # 存储到Milvus
            if self.collection:
                data = [
                    [embedding_id],  # id
                    [scene_id],      # scene_id
                    [embedding_type], # embedding_type
                    [text_content],   # text_content
                    [vector.tolist()] # vector
                ]
                
                self.collection.insert(data)
                self.collection.flush()
            
            # 存储到PostgreSQL
            vector_embedding = VectorEmbedding(
                id=embedding_id,
                scene_id=scene_id,
                embedding_type=embedding_type,
                vector_data={
                    "milvus_id": embedding_id,
                    "text_content": text_content,
                    "dimension": len(vector)
                },
                model_name=self._get_model_name(),
                model_version="1.0"
            )
            
            self.db.add(vector_embedding)
            self.db.commit()
            
            return {
                "embedding_id": embedding_id,
                "embedding_type": embedding_type,
                "text_length": len(text_content),
                "vector_dimension": len(vector)
            }
            
        except Exception as e:
            self.db.rollback()
            raise Exception(f"创建向量嵌入失败: {str(e)}")
    
    async def _generate_vector(self, text: str) -> np.ndarray:
        """生成文本向量"""
        if not self.embedding_model:
            # 返回随机向量用于测试
            return np.random.rand(self.dimension).astype(np.float32)
        
        try:
            if self.embedding_model == "openai":
                # 使用OpenAI API
                response = openai.Embedding.create(
                    input=text,
                    model="text-embedding-ada-002"
                )
                vector = np.array(response['data'][0]['embedding'], dtype=np.float32)
                
                # 如果维度不匹配，进行调整
                if len(vector) != self.dimension:
                    if len(vector) > self.dimension:
                        vector = vector[:self.dimension]
                    else:
                        # 填充零值
                        padded = np.zeros(self.dimension, dtype=np.float32)
                        padded[:len(vector)] = vector
                        vector = padded
                
                return vector
            else:
                # 使用sentence-transformers
                vector = self.embedding_model.encode(text, convert_to_numpy=True)
                return vector.astype(np.float32)
                
        except Exception as e:
            print(f"向量生成失败，使用随机向量: {e}")
            return np.random.rand(self.dimension).astype(np.float32)
    
    def _build_metadata_text(self, scene: Scene) -> str:
        """构建元数据文本用于向量化"""
        parts = []
        
        if scene.title:
            parts.append(f"标题: {scene.title}")
        
        if scene.characters:
            parts.append(f"人物: {', '.join(scene.characters)}")
        
        if scene.props:
            parts.append(f"道具: {', '.join(scene.props)}")
        
        if scene.assets:
            parts.append(f"资产: {', '.join(scene.assets)}")
        
        if scene.tags:
            parts.append(f"标签: {', '.join(scene.tags)}")
        
        if scene.location:
            parts.append(f"地点: {scene.location}")
        
        if scene.mood:
            parts.append(f"情绪: {scene.mood}")
        
        if scene.action_type:
            parts.append(f"动作类型: {scene.action_type}")
        
        return " ".join(parts)
    
    def _get_model_name(self) -> str:
        """获取模型名称"""
        if self.embedding_model == "openai":
            return "text-embedding-ada-002"
        elif hasattr(self.embedding_model, 'get_sentence_embedding_dimension'):
            return "paraphrase-multilingual-MiniLM-L12-v2"
        else:
            return "mock-model"
    
    async def search_similar_scenes(self, query_text: str, limit: int = 10) -> List[Dict[str, Any]]:
        """搜索相似场次"""
        try:
            if not self.collection:
                return []
            
            # 生成查询向量
            query_vector = await self._generate_vector(query_text)
            
            # 在Milvus中搜索
            search_params = {"metric_type": "COSINE", "params": {"nprobe": 10}}
            
            results = self.collection.search(
                data=[query_vector.tolist()],
                anns_field="vector",
                param=search_params,
                limit=limit,
                output_fields=["scene_id", "embedding_type", "text_content"]
            )
            
            # 处理搜索结果
            similar_scenes = []
            for hits in results:
                for hit in hits:
                    similar_scenes.append({
                        "scene_id": hit.entity.get("scene_id"),
                        "embedding_type": hit.entity.get("embedding_type"),
                        "text_content": hit.entity.get("text_content"),
                        "similarity_score": float(hit.score),
                        "distance": float(hit.distance)
                    })
            
            return similar_scenes
            
        except Exception as e:
            print(f"搜索失败: {e}")
            return []
