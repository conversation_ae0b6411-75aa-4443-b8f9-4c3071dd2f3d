"""
应用配置设置
"""
from pydantic_settings import BaseSettings
from typing import Optional
import os

class Settings(BaseSettings):
    """应用配置类"""
    
    # 基础配置
    app_name: str = "Video Scene Backend"
    debug: bool = False
    log_level: str = "INFO"
    
    # 数据库配置
    database_url: str = "postgresql://user:password@localhost:5432/video_db"
    redis_url: str = "redis://localhost:6379"
    
    # Milvus配置
    milvus_host: str = "localhost"
    milvus_port: int = 19530
    
    # AI服务配置
    openai_api_key: Optional[str] = None
    volcano_app_id: Optional[str] = None
    volcano_access_key: Optional[str] = None
    volcano_secret_key: Optional[str] = None
    
    # 项目配置
    project_root_dir: str = "./projects"
    
    # 安全配置
    secret_key: str = "your_secret_key_here"
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False

# 创建全局设置实例
get_settings = Settings()
settings = get_settings

# 确保项目根目录存在
os.makedirs(get_settings.project_root_dir, exist_ok=True)