"""
视频数据模型
"""
from sqlalchemy import Column, String, BigInteger, Integer, Numeric, DateTime, JSON, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.database.connection import Base
import uuid

class Video(Base):
    """视频模型"""
    __tablename__ = "videos"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey('projects.id', ondelete='CASCADE'), nullable=False, comment="项目ID")
    filename = Column(String(255), nullable=False, comment="文件名")
    relative_path = Column(String(500), nullable=False, comment="相对于项目目录的路径")
    file_size = Column(BigInteger, nullable=False, comment="文件大小(字节)")
    duration = Column(Numeric(10, 3), nullable=False, comment="视频时长(秒)")
    width = Column(Integer, comment="视频宽度")
    height = Column(Integer, comment="视频高度")
    fps = Column(Numeric(8, 3), comment="帧率")
    format = Column(String(50), comment="视频格式")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    last_scanned = Column(DateTime(timezone=True), server_default=func.now(), comment="最后扫描时间")
    processing_status = Column(String(50), default='pending', comment="处理状态")
    video_metadata = Column(JSON, default={}, comment="视频元数据")
    
    # 关系
    scenes = relationship("Scene", back_populates="video", cascade="all, delete-orphan")
    subtitles = relationship("Subtitle", back_populates="video", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Video(id={self.id}, filename='{self.filename}')>"
