"""
场次管理控制器
"""
from typing import List, Optional, Dict, Any
from uuid import UUID
from decimal import Decimal
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.models.scene import Scene
from app.models.video import Video
from app.models.project import Project
from app.models.schemas import SceneCreate, SceneUpdate

class SceneController:
    """场次管理控制器"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_scene(self, scene_data: SceneCreate, user_id: UUID) -> Scene:
        """创建场次"""
        # 验证视频权限
        video = self.db.query(Video).join(Project).filter(
            and_(
                Video.id == scene_data.video_id,
                Project.user_id == user_id,
                Project.status == 'active'
            )
        ).first()
        
        if not video:
            raise ValueError("视频不存在或无权限访问")
        
        # 创建场次
        scene = Scene(
            video_id=scene_data.video_id,
            start_time=scene_data.start_time,
            end_time=scene_data.end_time,
            title=scene_data.title,
            description=scene_data.description,
            characters=scene_data.characters or [],
            props=scene_data.props or [],
            assets=scene_data.assets or [],
            tags=scene_data.tags or [],
            location=scene_data.location,
            time_of_day=scene_data.time_of_day,
            weather=scene_data.weather,
            mood=scene_data.mood,
            action_type=scene_data.action_type,
            dialogue_summary=scene_data.dialogue_summary,
            visual_description=scene_data.visual_description,
            ai_generated=False,  # 用户手动创建
            human_verified=True,
            confidence_score=Decimal('1.0'),
            scene_metadata={
                "created_by": "user",
                "manual_creation": True
            }
        )
        
        self.db.add(scene)
        self.db.commit()
        self.db.refresh(scene)
        
        return scene
    
    def get_scene(self, scene_id: UUID, user_id: UUID) -> Optional[Scene]:
        """获取场次详情（带权限验证）"""
        return self.db.query(Scene).join(Video).join(Project).filter(
            and_(
                Scene.id == scene_id,
                Project.user_id == user_id,
                Project.status == 'active'
            )
        ).first()
    
    def get_video_scenes(self, video_id: UUID, user_id: UUID, 
                        skip: int = 0, limit: int = 100) -> List[Scene]:
        """获取视频的所有场次"""
        # 验证视频权限
        video = self.db.query(Video).join(Project).filter(
            and_(
                Video.id == video_id,
                Project.user_id == user_id,
                Project.status == 'active'
            )
        ).first()
        
        if not video:
            return []
        
        return self.db.query(Scene).filter(
            Scene.video_id == video_id
        ).order_by(Scene.start_time).offset(skip).limit(limit).all()
    
    def get_project_scenes(self, project_id: UUID, user_id: UUID,
                          skip: int = 0, limit: int = 100) -> List[Scene]:
        """获取项目的所有场次"""
        return self.db.query(Scene).join(Video).join(Project).filter(
            and_(
                Project.id == project_id,
                Project.user_id == user_id,
                Project.status == 'active'
            )
        ).order_by(Scene.start_time).offset(skip).limit(limit).all()
    
    def update_scene(self, scene_id: UUID, scene_data: SceneUpdate, user_id: UUID) -> Optional[Scene]:
        """更新场次"""
        scene = self.get_scene(scene_id, user_id)
        if not scene:
            return None
        
        # 更新字段
        update_data = scene_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(scene, field):
                setattr(scene, field, value)
        
        # 如果是AI生成的场次被用户修改，标记为人工验证
        if scene.ai_generated and not scene.human_verified:
            scene.human_verified = True
            if not scene.scene_metadata:
                scene.scene_metadata = {}
            scene.scene_metadata["human_modified"] = True
        
        self.db.commit()
        self.db.refresh(scene)
        
        return scene
    
    def delete_scene(self, scene_id: UUID, user_id: UUID) -> bool:
        """删除场次"""
        scene = self.get_scene(scene_id, user_id)
        if not scene:
            return False
        
        self.db.delete(scene)
        self.db.commit()
        return True
    
    def search_scenes(self, query: str, project_id: Optional[UUID] = None, 
                     user_id: UUID = None, skip: int = 0, limit: int = 50) -> List[Scene]:
        """搜索场次"""
        base_query = self.db.query(Scene).join(Video).join(Project).filter(
            and_(
                Project.user_id == user_id,
                Project.status == 'active'
            )
        )
        
        if project_id:
            base_query = base_query.filter(Project.id == project_id)
        
        # 文本搜索
        search_filter = or_(
            Scene.title.ilike(f"%{query}%"),
            Scene.description.ilike(f"%{query}%"),
            Scene.dialogue_summary.ilike(f"%{query}%"),
            Scene.visual_description.ilike(f"%{query}%"),
            Scene.location.ilike(f"%{query}%"),
            Scene.mood.ilike(f"%{query}%"),
            Scene.action_type.ilike(f"%{query}%")
        )
        
        return base_query.filter(search_filter).order_by(
            Scene.start_time
        ).offset(skip).limit(limit).all()
    
    def get_scenes_by_time_range(self, video_id: UUID, start_time: float, 
                                end_time: float, user_id: UUID) -> List[Scene]:
        """根据时间范围获取场次"""
        # 验证视频权限
        video = self.db.query(Video).join(Project).filter(
            and_(
                Video.id == video_id,
                Project.user_id == user_id,
                Project.status == 'active'
            )
        ).first()
        
        if not video:
            return []
        
        return self.db.query(Scene).filter(
            and_(
                Scene.video_id == video_id,
                or_(
                    and_(Scene.start_time >= start_time, Scene.start_time <= end_time),
                    and_(Scene.end_time >= start_time, Scene.end_time <= end_time),
                    and_(Scene.start_time <= start_time, Scene.end_time >= end_time)
                )
            )
        ).order_by(Scene.start_time).all()
    
    def get_scenes_by_characters(self, characters: List[str], project_id: Optional[UUID] = None,
                               user_id: UUID = None, skip: int = 0, limit: int = 50) -> List[Scene]:
        """根据人物获取场次"""
        base_query = self.db.query(Scene).join(Video).join(Project).filter(
            and_(
                Project.user_id == user_id,
                Project.status == 'active'
            )
        )
        
        if project_id:
            base_query = base_query.filter(Project.id == project_id)
        
        # 搜索包含指定人物的场次
        for character in characters:
            base_query = base_query.filter(Scene.characters.contains([character]))
        
        return base_query.order_by(Scene.start_time).offset(skip).limit(limit).all()
    
    def get_scenes_by_tags(self, tags: List[str], project_id: Optional[UUID] = None,
                          user_id: UUID = None, skip: int = 0, limit: int = 50) -> List[Scene]:
        """根据标签获取场次"""
        base_query = self.db.query(Scene).join(Video).join(Project).filter(
            and_(
                Project.user_id == user_id,
                Project.status == 'active'
            )
        )
        
        if project_id:
            base_query = base_query.filter(Project.id == project_id)
        
        # 搜索包含指定标签的场次
        for tag in tags:
            base_query = base_query.filter(Scene.tags.contains([tag]))
        
        return base_query.order_by(Scene.start_time).offset(skip).limit(limit).all()
    
    def get_scene_stats(self, project_id: UUID, user_id: UUID) -> Dict[str, Any]:
        """获取项目场次统计"""
        scenes = self.get_project_scenes(project_id, user_id, limit=10000)
        
        if not scenes:
            return {}
        
        # 统计信息
        total_scenes = len(scenes)
        ai_generated = len([s for s in scenes if s.ai_generated])
        human_verified = len([s for s in scenes if s.human_verified])
        
        # 统计标签
        all_tags = []
        for scene in scenes:
            if scene.tags:
                all_tags.extend(scene.tags)
        
        tag_counts = {}
        for tag in all_tags:
            tag_counts[tag] = tag_counts.get(tag, 0) + 1
        
        # 统计人物
        all_characters = []
        for scene in scenes:
            if scene.characters:
                all_characters.extend(scene.characters)
        
        character_counts = {}
        for character in all_characters:
            character_counts[character] = character_counts.get(character, 0) + 1
        
        # 统计情绪
        mood_counts = {}
        for scene in scenes:
            if scene.mood:
                mood_counts[scene.mood] = mood_counts.get(scene.mood, 0) + 1
        
        return {
            "project_id": str(project_id),
            "total_scenes": total_scenes,
            "ai_generated": ai_generated,
            "human_verified": human_verified,
            "verification_rate": human_verified / total_scenes if total_scenes > 0 else 0,
            "top_tags": sorted(tag_counts.items(), key=lambda x: x[1], reverse=True)[:10],
            "top_characters": sorted(character_counts.items(), key=lambda x: x[1], reverse=True)[:10],
            "mood_distribution": mood_counts
        }
