"""
项目数据模型
"""
from sqlalchemy import Column, String, Text, DateTime, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from app.database.connection import Base
import uuid

class Project(Base):
    """项目模型"""
    __tablename__ = "projects"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False, comment="项目名称")
    description = Column(Text, comment="项目描述")
    user_id = Column(UUID(as_uuid=True), nullable=False, comment="用户ID")
    resource_path = Column(String(500), nullable=False, comment="项目资源目录路径")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    status = Column(String(50), default='active', comment="项目状态")
    settings = Column(JSON, default={}, comment="项目设置")
    
    def __repr__(self):
        return f"<Project(id={self.id}, name='{self.name}')>"
