"""
视频管理路由
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List
from uuid import UUID
from app.database.connection import get_db
from app.controllers.video_controller import VideoController
from app.models.schemas import VideoResponse

router = APIRouter()

def get_video_controller(db: Session = Depends(get_db)) -> VideoController:
    """获取视频控制器实例"""
    return VideoController(db)

@router.post("/projects/{project_id}/scan")
async def scan_project_videos(
    project_id: UUID,
    user_id: UUID = Query(..., description="用户ID"),
    controller: VideoController = Depends(get_video_controller)
):
    """扫描项目目录中的视频"""
    try:
        result = controller.scan_project_videos(project_id, user_id)
        return result
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"扫描失败: {str(e)}")

@router.get("/projects/{project_id}/videos", response_model=List[VideoResponse])
async def get_project_videos(
    project_id: UUID,
    user_id: UUID = Query(..., description="用户ID"),
    skip: int = Query(0, ge=0, description="跳过数量"),
    limit: int = Query(100, ge=1, le=100, description="返回数量"),
    controller: VideoController = Depends(get_video_controller)
):
    """获取项目视频列表"""
    videos = controller.get_project_videos(project_id, user_id, skip, limit)
    return [VideoResponse.model_validate(video) for video in videos]

@router.get("/videos/{video_id}", response_model=VideoResponse)
async def get_video(
    video_id: UUID,
    user_id: UUID = Query(..., description="用户ID"),
    controller: VideoController = Depends(get_video_controller)
):
    """获取视频详情"""
    video = controller.get_video_with_permission(video_id, user_id)
    if not video:
        raise HTTPException(status_code=404, detail="视频不存在")
    return VideoResponse.model_validate(video)

@router.delete("/videos/{video_id}")
async def delete_video(
    video_id: UUID,
    user_id: UUID = Query(..., description="用户ID"),
    controller: VideoController = Depends(get_video_controller)
):
    """删除视频记录"""
    success = controller.delete_video(video_id, user_id)
    if not success:
        raise HTTPException(status_code=404, detail="视频不存在")
    return {"message": "视频删除成功"}

@router.get("/videos/{video_id}/processing-status")
async def get_video_processing_status(
    video_id: UUID,
    user_id: UUID = Query(..., description="用户ID"),
    controller: VideoController = Depends(get_video_controller)
):
    """获取视频处理状态"""
    status = controller.get_video_processing_status(video_id, user_id)
    if not status:
        raise HTTPException(status_code=404, detail="视频不存在")
    return status

@router.get("/projects/{project_id}/videos/stats")
async def get_project_video_stats(
    project_id: UUID,
    user_id: UUID = Query(..., description="用户ID"),
    controller: VideoController = Depends(get_video_controller)
):
    """获取项目视频统计"""
    stats = controller.get_video_stats(project_id, user_id)
    if not stats:
        raise HTTPException(status_code=404, detail="项目不存在")
    return stats

@router.post("/videos/{video_id}/process")
async def process_video_complete(
    video_id: UUID,
    user_id: UUID = Query(..., description="用户ID"),
    controller: VideoController = Depends(get_video_controller)
):
    """完整处理视频：语音识别 + AI场次分割 + 向量化"""
    try:
        result = await controller.process_video_complete(video_id, user_id)
        return result
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理失败: {str(e)}")

@router.post("/videos/{video_id}/process/speech")
async def process_video_speech(
    video_id: UUID,
    user_id: UUID = Query(..., description="用户ID"),
    controller: VideoController = Depends(get_video_controller)
):
    """仅处理视频语音识别"""
    try:
        result = await controller.process_video_speech_only(video_id, user_id)
        return result
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"语音识别失败: {str(e)}")

@router.post("/videos/{video_id}/process/scenes")
async def process_video_scenes(
    video_id: UUID,
    user_id: UUID = Query(..., description="用户ID"),
    controller: VideoController = Depends(get_video_controller)
):
    """仅处理视频场次分析"""
    try:
        result = await controller.process_video_scenes_only(video_id, user_id)
        return result
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"场次分析失败: {str(e)}")
