"""
字幕管理控制器
"""
from typing import List, Optional, Dict, Any
from uuid import UUID
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.models.scene import Subtitle
from app.models.video import Video
from app.models.project import Project
from app.models.schemas import SubtitleCreate, SubtitleUpdate

class SubtitleController:
    """字幕管理控制器"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_subtitle(self, subtitle_data: SubtitleCreate, user_id: UUID) -> Subtitle:
        """创建字幕"""
        # 验证视频权限
        video = self.db.query(Video).join(Project).filter(
            and_(
                Video.id == subtitle_data.video_id,
                Project.user_id == user_id,
                Project.status == 'active'
            )
        ).first()
        
        if not video:
            raise ValueError("视频不存在或无权限访问")
        
        # 创建字幕
        subtitle = Subtitle(
            video_id=subtitle_data.video_id,
            start_time=subtitle_data.start_time,
            end_time=subtitle_data.end_time,
            text=subtitle_data.text,
            speaker=subtitle_data.speaker,
            confidence=subtitle_data.confidence,
            language=subtitle_data.language or 'zh-CN',
            source=subtitle_data.source or 'manual'
        )
        
        self.db.add(subtitle)
        self.db.commit()
        self.db.refresh(subtitle)
        
        return subtitle
    
    def get_subtitle(self, subtitle_id: UUID, user_id: UUID) -> Optional[Subtitle]:
        """获取字幕详情（带权限验证）"""
        return self.db.query(Subtitle).join(Video).join(Project).filter(
            and_(
                Subtitle.id == subtitle_id,
                Project.user_id == user_id,
                Project.status == 'active'
            )
        ).first()
    
    def get_video_subtitles(self, video_id: UUID, user_id: UUID, 
                           skip: int = 0, limit: int = 1000) -> List[Subtitle]:
        """获取视频的所有字幕"""
        # 验证视频权限
        video = self.db.query(Video).join(Project).filter(
            and_(
                Video.id == video_id,
                Project.user_id == user_id,
                Project.status == 'active'
            )
        ).first()
        
        if not video:
            return []
        
        return self.db.query(Subtitle).filter(
            Subtitle.video_id == video_id
        ).order_by(Subtitle.start_time).offset(skip).limit(limit).all()
    
    def get_subtitles_by_time_range(self, video_id: UUID, start_time: float, 
                                   end_time: float, user_id: UUID) -> List[Subtitle]:
        """根据时间范围获取字幕"""
        # 验证视频权限
        video = self.db.query(Video).join(Project).filter(
            and_(
                Video.id == video_id,
                Project.user_id == user_id,
                Project.status == 'active'
            )
        ).first()
        
        if not video:
            return []
        
        return self.db.query(Subtitle).filter(
            and_(
                Subtitle.video_id == video_id,
                or_(
                    and_(Subtitle.start_time >= start_time, Subtitle.start_time <= end_time),
                    and_(Subtitle.end_time >= start_time, Subtitle.end_time <= end_time),
                    and_(Subtitle.start_time <= start_time, Subtitle.end_time >= end_time)
                )
            )
        ).order_by(Subtitle.start_time).all()
    
    def update_subtitle(self, subtitle_id: UUID, subtitle_data: SubtitleUpdate, user_id: UUID) -> Optional[Subtitle]:
        """更新字幕"""
        subtitle = self.get_subtitle(subtitle_id, user_id)
        if not subtitle:
            return None
        
        # 更新字段
        update_data = subtitle_data.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            if hasattr(subtitle, field):
                setattr(subtitle, field, value)
        
        self.db.commit()
        self.db.refresh(subtitle)
        
        return subtitle
    
    def delete_subtitle(self, subtitle_id: UUID, user_id: UUID) -> bool:
        """删除字幕"""
        subtitle = self.get_subtitle(subtitle_id, user_id)
        if not subtitle:
            return False
        
        self.db.delete(subtitle)
        self.db.commit()
        return True
    
    def search_subtitles(self, query: str, video_id: Optional[UUID] = None, 
                        user_id: UUID = None, skip: int = 0, limit: int = 100) -> List[Subtitle]:
        """搜索字幕"""
        base_query = self.db.query(Subtitle).join(Video).join(Project).filter(
            and_(
                Project.user_id == user_id,
                Project.status == 'active'
            )
        )
        
        if video_id:
            base_query = base_query.filter(Subtitle.video_id == video_id)
        
        # 文本搜索
        search_filter = or_(
            Subtitle.text.ilike(f"%{query}%"),
            Subtitle.speaker.ilike(f"%{query}%")
        )
        
        return base_query.filter(search_filter).order_by(
            Subtitle.start_time
        ).offset(skip).limit(limit).all()
    
    def get_subtitles_by_speaker(self, speaker: str, video_id: Optional[UUID] = None,
                                user_id: UUID = None, skip: int = 0, limit: int = 100) -> List[Subtitle]:
        """根据说话人获取字幕"""
        base_query = self.db.query(Subtitle).join(Video).join(Project).filter(
            and_(
                Project.user_id == user_id,
                Project.status == 'active'
            )
        )
        
        if video_id:
            base_query = base_query.filter(Subtitle.video_id == video_id)
        
        return base_query.filter(
            Subtitle.speaker.ilike(f"%{speaker}%")
        ).order_by(Subtitle.start_time).offset(skip).limit(limit).all()
    
    def export_subtitles_srt(self, video_id: UUID, user_id: UUID) -> str:
        """导出SRT格式字幕"""
        subtitles = self.get_video_subtitles(video_id, user_id, limit=10000)
        
        if not subtitles:
            return ""
        
        srt_content = []
        for i, subtitle in enumerate(subtitles, 1):
            # 格式化时间
            start_time = self._format_srt_time(subtitle.start_time)
            end_time = self._format_srt_time(subtitle.end_time)
            
            # 添加说话人信息
            text = subtitle.text
            if subtitle.speaker:
                text = f"[{subtitle.speaker}] {text}"
            
            srt_content.append(f"{i}")
            srt_content.append(f"{start_time} --> {end_time}")
            srt_content.append(text)
            srt_content.append("")  # 空行
        
        return "\n".join(srt_content)
    
    def export_subtitles_vtt(self, video_id: UUID, user_id: UUID) -> str:
        """导出VTT格式字幕"""
        subtitles = self.get_video_subtitles(video_id, user_id, limit=10000)
        
        if not subtitles:
            return "WEBVTT\n\n"
        
        vtt_content = ["WEBVTT", ""]
        
        for subtitle in subtitles:
            # 格式化时间
            start_time = self._format_vtt_time(subtitle.start_time)
            end_time = self._format_vtt_time(subtitle.end_time)
            
            # 添加说话人信息
            text = subtitle.text
            if subtitle.speaker:
                text = f"<v {subtitle.speaker}>{text}"
            
            vtt_content.append(f"{start_time} --> {end_time}")
            vtt_content.append(text)
            vtt_content.append("")  # 空行
        
        return "\n".join(vtt_content)
    
    def _format_srt_time(self, seconds: float) -> str:
        """格式化SRT时间格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        milliseconds = int((seconds % 1) * 1000)
        
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{milliseconds:03d}"
    
    def _format_vtt_time(self, seconds: float) -> str:
        """格式化VTT时间格式"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        
        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{secs:06.3f}"
        else:
            return f"{minutes:02d}:{secs:06.3f}"
    
    def get_subtitle_stats(self, video_id: UUID, user_id: UUID) -> Dict[str, Any]:
        """获取视频字幕统计"""
        subtitles = self.get_video_subtitles(video_id, user_id, limit=10000)
        
        if not subtitles:
            return {}
        
        # 统计信息
        total_subtitles = len(subtitles)
        total_duration = sum(sub.end_time - sub.start_time for sub in subtitles)
        total_characters = sum(len(sub.text) for sub in subtitles)
        
        # 统计说话人
        speakers = {}
        for subtitle in subtitles:
            if subtitle.speaker:
                speakers[subtitle.speaker] = speakers.get(subtitle.speaker, 0) + 1
        
        # 统计置信度
        confidence_scores = [sub.confidence for sub in subtitles if sub.confidence is not None]
        avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0
        
        # 统计来源
        sources = {}
        for subtitle in subtitles:
            source = subtitle.source or 'unknown'
            sources[source] = sources.get(source, 0) + 1
        
        return {
            "video_id": str(video_id),
            "total_subtitles": total_subtitles,
            "total_duration": total_duration,
            "total_characters": total_characters,
            "average_confidence": avg_confidence,
            "speakers": speakers,
            "sources": sources,
            "average_subtitle_length": total_characters / total_subtitles if total_subtitles > 0 else 0
        }
