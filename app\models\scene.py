"""
场次数据模型
"""
from sqlalchemy import Column, String, Text, Integer, Numeric, Boolean, DateTime, JSON, ForeignKey, ARRAY
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.database.connection import Base
import uuid

class Scene(Base):
    """场次模型"""
    __tablename__ = "scenes"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    video_id = Column(UUID(as_uuid=True), ForeignKey('videos.id', ondelete='CASCADE'), nullable=False, comment="视频ID")
    scene_number = Column(Integer, nullable=False, comment="场次编号")
    start_time = Column(Numeric(10, 3), nullable=False, comment="开始时间(秒)")
    end_time = Column(Numeric(10, 3), nullable=False, comment="结束时间(秒)")
    title = Column(String(255), comment="场次标题")
    description = Column(Text, comment="场次描述")
    location = Column(String(255), comment="拍摄地点")
    time_of_day = Column(String(50), comment="时间段")
    characters = Column(ARRAY(Text), comment="角色列表")
    props = Column(ARRAY(Text), comment="道具列表")
    assets = Column(ARRAY(Text), comment="资产列表")
    tags = Column(ARRAY(Text), comment="标签列表")
    mood = Column(String(100), comment="情绪氛围")
    action_type = Column(String(100), comment="动作类型")
    dialogue_summary = Column(Text, comment="对话摘要")
    visual_description = Column(Text, comment="视觉描述")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    ai_generated = Column(Boolean, default=True, comment="是否AI生成")
    human_verified = Column(Boolean, default=False, comment="是否人工验证")
    confidence_score = Column(Numeric(3, 2), comment="置信度分数")
    scene_metadata = Column(JSON, default={}, comment="场次元数据")
    
    # 关系
    video = relationship("Video", back_populates="scenes")
    vector_embeddings = relationship("VectorEmbedding", back_populates="scene", cascade="all, delete-orphan")
    favorites = relationship("SceneFavorite", back_populates="scene", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Scene(id={self.id}, scene_number={self.scene_number}, title='{self.title}')>"

class Subtitle(Base):
    """字幕模型"""
    __tablename__ = "subtitles"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    video_id = Column(UUID(as_uuid=True), ForeignKey('videos.id', ondelete='CASCADE'), nullable=False, comment="视频ID")
    start_time = Column(Numeric(10, 3), nullable=False, comment="开始时间(秒)")
    end_time = Column(Numeric(10, 3), nullable=False, comment="结束时间(秒)")
    text = Column(Text, nullable=False, comment="字幕文本")
    speaker = Column(String(255), comment="说话人")
    confidence = Column(Numeric(3, 2), comment="识别置信度")
    language = Column(String(10), default='zh-CN', comment="语言")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    
    # 关系
    video = relationship("Video", back_populates="subtitles")
    
    def __repr__(self):
        return f"<Subtitle(id={self.id}, text='{self.text[:50]}...')>"
