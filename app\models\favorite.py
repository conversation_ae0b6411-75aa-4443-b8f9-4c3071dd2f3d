"""
收藏和向量嵌入数据模型
"""
from sqlalchemy import Column, String, Text, DateTime, ForeignKey, ARRAY, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.database.connection import Base
import uuid

class VectorEmbedding(Base):
    """向量嵌入模型"""
    __tablename__ = "vector_embeddings"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    scene_id = Column(UUID(as_uuid=True), ForeignKey('scenes.id', ondelete='CASCADE'), nullable=False, comment="场次ID")
    embedding_type = Column(String(50), nullable=False, comment="嵌入类型: text, visual, combined")
    vector_id = Column(String(255), nullable=False, comment="Milvus中的向量ID")
    content_hash = Column(String(64), nullable=False, comment="内容哈希")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    
    # 关系
    scene = relationship("Scene", back_populates="vector_embeddings")
    
    def __repr__(self):
        return f"<VectorEmbedding(id={self.id}, type='{self.embedding_type}')>"

class SceneFavorite(Base):
    """场次收藏模型"""
    __tablename__ = "scene_favorites"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey('projects.id', ondelete='CASCADE'), nullable=False, comment="项目ID")
    scene_id = Column(UUID(as_uuid=True), ForeignKey('scenes.id', ondelete='CASCADE'), nullable=False, comment="场次ID")
    user_id = Column(UUID(as_uuid=True), nullable=False, comment="用户ID")
    notes = Column(Text, comment="用户备注")
    tags = Column(ARRAY(Text), comment="用户自定义标签")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关系
    scene = relationship("Scene", back_populates="favorites")
    
    # 唯一约束：同一项目中场次不能重复收藏
    __table_args__ = (
        UniqueConstraint('project_id', 'scene_id', name='uq_project_scene_favorite'),
    )
    
    def __repr__(self):
        return f"<SceneFavorite(id={self.id}, project_id={self.project_id}, scene_id={self.scene_id})>"
