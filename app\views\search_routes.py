"""
搜索路由
"""
from fastapi import APIRouter, Depends, HTTPException, Query, Body
from sqlalchemy.orm import Session
from typing import Optional, Dict, Any
from uuid import UUID

from app.database.connection import get_db
from app.controllers.search_controller import SearchController

router = APIRouter()

def get_search_controller(db: Session = Depends(get_db)) -> SearchController:
    """获取搜索控制器实例"""
    return SearchController(db)

@router.get("/search/scenes")
async def search_scenes(
    q: str = Query(..., description="搜索关键词"),
    user_id: UUID = Query(..., description="用户ID"),
    project_id: Optional[UUID] = Query(None, description="项目ID"),
    limit: int = Query(20, ge=1, le=100, description="返回数量"),
    controller: SearchController = Depends(get_search_controller)
):
    """搜索场次（混合搜索）"""
    try:
        result = await controller.search_scenes(q, user_id, project_id, limit)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

@router.get("/search/subtitles")
async def search_subtitles(
    q: str = Query(..., description="搜索关键词"),
    user_id: UUID = Query(..., description="用户ID"),
    video_id: Optional[UUID] = Query(None, description="视频ID"),
    project_id: Optional[UUID] = Query(None, description="项目ID"),
    limit: int = Query(50, ge=1, le=100, description="返回数量"),
    controller: SearchController = Depends(get_search_controller)
):
    """搜索字幕"""
    try:
        result = await controller.search_subtitles(q, user_id, video_id, project_id, limit)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

@router.get("/search/suggestions")
async def get_search_suggestions(
    q: str = Query(..., description="搜索关键词"),
    user_id: UUID = Query(..., description="用户ID"),
    project_id: Optional[UUID] = Query(None, description="项目ID"),
    limit: int = Query(10, ge=1, le=20, description="返回数量"),
    controller: SearchController = Depends(get_search_controller)
):
    """获取搜索建议"""
    try:
        result = await controller.get_search_suggestions(q, user_id, project_id, limit)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取建议失败: {str(e)}")

@router.post("/search/scenes/advanced")
async def advanced_search_scenes(
    filters: Dict[str, Any] = Body(..., description="高级搜索过滤条件"),
    user_id: UUID = Query(..., description="用户ID"),
    project_id: Optional[UUID] = Query(None, description="项目ID"),
    limit: int = Query(50, ge=1, le=100, description="返回数量"),
    controller: SearchController = Depends(get_search_controller)
):
    """高级搜索场次"""
    try:
        result = await controller.advanced_search_scenes(filters, user_id, project_id, limit)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"高级搜索失败: {str(e)}")

@router.get("/search/stats")
async def get_search_stats(
    user_id: UUID = Query(..., description="用户ID"),
    project_id: Optional[UUID] = Query(None, description="项目ID"),
    controller: SearchController = Depends(get_search_controller)
):
    """获取搜索统计信息"""
    try:
        from app.models.scene import Scene
        from app.models.scene import Subtitle
        from app.models.video import Video
        from app.models.project import Project
        from sqlalchemy import and_, func

        # 统计场次数量
        scene_query = controller.db.query(Scene).join(Video).join(Project).filter(
            and_(
                Project.user_id == user_id,
                Project.status == 'active'
            )
        )

        if project_id:
            scene_query = scene_query.filter(Project.id == project_id)

        total_scenes = scene_query.count()
        ai_generated_scenes = scene_query.filter(Scene.ai_generated == True).count()
        verified_scenes = scene_query.filter(Scene.human_verified == True).count()

        # 统计字幕数量
        subtitle_query = controller.db.query(Subtitle).join(Video).join(Project).filter(
            and_(
                Project.user_id == user_id,
                Project.status == 'active'
            )
        )

        if project_id:
            subtitle_query = subtitle_query.filter(Project.id == project_id)

        total_subtitles = subtitle_query.count()

        return {
            "user_id": str(user_id),
            "project_id": str(project_id) if project_id else None,
            "total_scenes": total_scenes,
            "ai_generated_scenes": ai_generated_scenes,
            "verified_scenes": verified_scenes,
            "total_subtitles": total_subtitles,
            "searchable_content": {
                "scenes": total_scenes,
                "subtitles": total_subtitles
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计失败: {str(e)}")
