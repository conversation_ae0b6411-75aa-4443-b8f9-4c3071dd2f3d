"""
视频管理控制器
"""
import asyncio
from typing import List, Optional, Dict, Any
from uuid import UUID
from sqlalchemy.orm import Session
from sqlalchemy import and_
from app.models.video import Video
from app.models.project import Project
from app.services.video_scanner import VideoScanner
from app.services.speech_service import SpeechRecognitionService
from app.services.ai_scene_service import AISceneService
from app.services.vector_service import VectorService

class VideoController:
    """视频管理控制器"""
    
    def __init__(self, db: Session):
        self.db = db
        self.scanner = VideoScanner(db)
        self.speech_service = SpeechRecognitionService(db)
        self.ai_scene_service = AISceneService(db)
        self.vector_service = VectorService(db)
    
    def scan_project_videos(self, project_id: UUID, user_id: UUID) -> Dict:
        """扫描项目视频"""
        # 验证项目权限
        project = self.db.query(Project).filter(
            and_(
                Project.id == project_id,
                Project.user_id == user_id,
                Project.status == 'active'
            )
        ).first()
        
        if not project:
            raise ValueError("项目不存在或无权限访问")
        
        # 执行扫描
        scan_results = self.scanner.scan_project_directory(str(project_id))
        
        # 统计结果
        stats = {
            "total_files": len(scan_results),
            "added": len([r for r in scan_results if r["status"] == "added"]),
            "exists": len([r for r in scan_results if r["status"] == "exists"]),
            "errors": len([r for r in scan_results if r["status"] == "error"])
        }
        
        return {
            "project_id": str(project_id),
            "scan_results": scan_results,
            "statistics": stats
        }
    
    def get_project_videos(self, project_id: UUID, user_id: UUID, skip: int = 0, limit: int = 100) -> List[Video]:
        """获取项目视频列表"""
        # 验证项目权限
        project = self.db.query(Project).filter(
            and_(
                Project.id == project_id,
                Project.user_id == user_id,
                Project.status == 'active'
            )
        ).first()
        
        if not project:
            return []
        
        return self.db.query(Video).filter(
            Video.project_id == project_id
        ).offset(skip).limit(limit).all()
    
    def get_video(self, video_id: UUID) -> Optional[Video]:
        """获取视频详情"""
        return self.db.query(Video).filter(Video.id == video_id).first()
    
    def get_video_with_permission(self, video_id: UUID, user_id: UUID) -> Optional[Video]:
        """获取视频详情（带权限验证）"""
        return self.db.query(Video).join(Project).filter(
            and_(
                Video.id == video_id,
                Project.user_id == user_id,
                Project.status == 'active'
            )
        ).first()
    
    def delete_video(self, video_id: UUID, user_id: UUID) -> bool:
        """删除视频记录"""
        video = self.get_video_with_permission(video_id, user_id)
        if not video:
            return False
        
        self.db.delete(video)
        self.db.commit()
        return True
    
    def update_video_processing_status(self, video_id: UUID, status: str) -> bool:
        """更新视频处理状态"""
        video = self.get_video(video_id)
        if not video:
            return False
        
        video.processing_status = status
        self.db.commit()
        return True
    
    def get_video_processing_status(self, video_id: UUID, user_id: UUID) -> Optional[Dict]:
        """获取视频处理状态"""
        video = self.get_video_with_permission(video_id, user_id)
        if not video:
            return None
        
        return {
            "video_id": str(video.id),
            "filename": video.filename,
            "processing_status": video.processing_status,
            "duration": float(video.duration),
            "created_at": video.created_at.isoformat(),
            "last_scanned": video.last_scanned.isoformat() if video.last_scanned else None
        }
    
    def get_video_stats(self, project_id: UUID, user_id: UUID) -> Dict:
        """获取项目视频统计"""
        # 验证项目权限
        project = self.db.query(Project).filter(
            and_(
                Project.id == project_id,
                Project.user_id == user_id,
                Project.status == 'active'
            )
        ).first()
        
        if not project:
            return {}
        
        videos = self.db.query(Video).filter(Video.project_id == project_id).all()
        
        total_duration = sum(float(video.duration) for video in videos)
        total_size = sum(video.file_size for video in videos)
        
        status_counts = {}
        for video in videos:
            status = video.processing_status
            status_counts[status] = status_counts.get(status, 0) + 1
        
        return {
            "project_id": str(project_id),
            "total_videos": len(videos),
            "total_duration": total_duration,
            "total_size": total_size,
            "status_distribution": status_counts,
            "average_duration": total_duration / len(videos) if videos else 0
        }

    async def process_video_complete(self, video_id: UUID, user_id: UUID) -> Dict[str, Any]:
        """完整处理视频：语音识别 + AI场次分割 + 向量化"""
        try:
            # 验证权限
            video = self.get_video_with_permission(video_id, user_id)
            if not video:
                raise ValueError("视频不存在或无权限访问")

            # 更新状态为处理中
            self.update_video_processing_status(video_id, "processing")

            results = {
                "video_id": str(video_id),
                "steps": []
            }

            # 步骤1: 语音识别
            try:
                speech_result = await self.speech_service.process_video_speech(str(video_id))
                results["steps"].append({
                    "step": "speech_recognition",
                    "status": speech_result.get("status", "error"),
                    "details": speech_result
                })

                if speech_result.get("status") != "success":
                    raise Exception(f"语音识别失败: {speech_result.get('error', 'Unknown error')}")

            except Exception as e:
                results["steps"].append({
                    "step": "speech_recognition",
                    "status": "error",
                    "error": str(e)
                })
                self.update_video_processing_status(video_id, "failed")
                return results

            # 步骤2: AI场次分割
            try:
                scene_result = await self.ai_scene_service.process_video_scenes(str(video_id))
                results["steps"].append({
                    "step": "scene_analysis",
                    "status": scene_result.get("status", "error"),
                    "details": scene_result
                })

                if scene_result.get("status") != "success":
                    raise Exception(f"场次分析失败: {scene_result.get('error', 'Unknown error')}")

            except Exception as e:
                results["steps"].append({
                    "step": "scene_analysis",
                    "status": "error",
                    "error": str(e)
                })
                self.update_video_processing_status(video_id, "failed")
                return results

            # 步骤3: 向量化处理
            try:
                # 为每个创建的场次生成向量
                scene_ids = [scene["id"] for scene in scene_result.get("scenes", [])]
                vector_results = []

                for scene_id in scene_ids:
                    vector_result = await self.vector_service.process_scene_embeddings(scene_id)
                    vector_results.append(vector_result)

                results["steps"].append({
                    "step": "vectorization",
                    "status": "success",
                    "details": {
                        "scenes_processed": len(vector_results),
                        "results": vector_results
                    }
                })

            except Exception as e:
                results["steps"].append({
                    "step": "vectorization",
                    "status": "error",
                    "error": str(e)
                })
                # 向量化失败不影响整体流程

            # 更新状态为完成
            self.update_video_processing_status(video_id, "completed")
            results["overall_status"] = "success"

            return results

        except Exception as e:
            self.update_video_processing_status(video_id, "failed")
            return {
                "video_id": str(video_id),
                "overall_status": "error",
                "error": str(e)
            }

    async def process_video_speech_only(self, video_id: UUID, user_id: UUID) -> Dict[str, Any]:
        """仅处理视频语音识别"""
        try:
            # 验证权限
            video = self.get_video_with_permission(video_id, user_id)
            if not video:
                raise ValueError("视频不存在或无权限访问")

            # 调用语音识别服务
            result = await self.speech_service.process_video_speech(str(video_id))

            return {
                "video_id": str(video_id),
                "step": "speech_recognition",
                "result": result
            }

        except Exception as e:
            return {
                "video_id": str(video_id),
                "step": "speech_recognition",
                "status": "error",
                "error": str(e)
            }

    async def process_video_scenes_only(self, video_id: UUID, user_id: UUID) -> Dict[str, Any]:
        """仅处理视频场次分析"""
        try:
            # 验证权限
            video = self.get_video_with_permission(video_id, user_id)
            if not video:
                raise ValueError("视频不存在或无权限访问")

            # 调用AI场次分析服务
            result = await self.ai_scene_service.process_video_scenes(str(video_id))

            return {
                "video_id": str(video_id),
                "step": "scene_analysis",
                "result": result
            }

        except Exception as e:
            return {
                "video_id": str(video_id),
                "step": "scene_analysis",
                "status": "error",
                "error": str(e)
            }
