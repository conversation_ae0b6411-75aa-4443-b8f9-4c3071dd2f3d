"""
视频扫描服务
"""
import os
import hashlib
from typing import List, Dict, Optional, Tuple
from pathlib import Path
import mimetypes
from datetime import datetime
from sqlalchemy.orm import Session

# 注意：在实际部署时需要安装ffmpeg-python
# 这里先用模拟数据，避免依赖问题
try:
    import ffmpeg
    FFMPEG_AVAILABLE = True
except ImportError:
    FFMPEG_AVAILABLE = False
    print("Warning: ffmpeg-python not available, using mock video metadata")

from app.models.video import Video
from app.models.project import Project

class VideoScanner:
    """视频扫描器"""
    
    # 支持的视频格式
    SUPPORTED_FORMATS = {
        '.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', 
        '.webm', '.m4v', '.3gp', '.ogv', '.ts', '.mts'
    }
    
    def __init__(self, db: Session):
        self.db = db
    
    def scan_project_directory(self, project_id: str) -> List[Dict]:
        """扫描项目目录中的视频文件"""
        # 获取项目信息
        project = self.db.query(Project).filter(Project.id == project_id).first()
        if not project:
            raise ValueError(f"项目 {project_id} 不存在")
        
        video_dir = os.path.join(project.resource_path, "videos")
        if not os.path.exists(video_dir):
            os.makedirs(video_dir, exist_ok=True)
            return []
        
        # 扫描视频文件
        video_files = self._find_video_files(video_dir)
        results = []
        
        for file_path in video_files:
            try:
                # 检查文件是否已存在于数据库
                relative_path = os.path.relpath(file_path, project.resource_path)
                existing_video = self.db.query(Video).filter(
                    Video.project_id == project_id,
                    Video.relative_path == relative_path
                ).first()
                
                if existing_video:
                    # 更新最后扫描时间
                    existing_video.last_scanned = datetime.utcnow()
                    self.db.commit()
                    results.append({
                        "status": "exists",
                        "video_id": str(existing_video.id),
                        "filename": existing_video.filename
                    })
                    continue
                
                # 提取视频元数据
                metadata = self._extract_video_metadata(file_path)
                if not metadata:
                    results.append({
                        "status": "error",
                        "filename": os.path.basename(file_path),
                        "error": "无法提取视频元数据"
                    })
                    continue
                
                # 创建视频记录
                video = Video(
                    project_id=project_id,
                    filename=os.path.basename(file_path),
                    relative_path=relative_path,
                    file_size=metadata['file_size'],
                    duration=metadata['duration'],
                    width=metadata.get('width'),
                    height=metadata.get('height'),
                    fps=metadata.get('fps'),
                    format=metadata.get('format'),
                    video_metadata=metadata
                )
                
                self.db.add(video)
                self.db.commit()
                self.db.refresh(video)
                
                results.append({
                    "status": "added",
                    "video_id": str(video.id),
                    "filename": video.filename,
                    "duration": float(video.duration),
                    "size": video.file_size
                })
                
            except Exception as e:
                results.append({
                    "status": "error",
                    "filename": os.path.basename(file_path),
                    "error": str(e)
                })
        
        return results
    
    def _find_video_files(self, directory: str) -> List[str]:
        """查找目录中的视频文件"""
        video_files = []
        
        for root, dirs, files in os.walk(directory):
            for file in files:
                file_path = os.path.join(root, file)
                file_ext = Path(file).suffix.lower()
                
                if file_ext in self.SUPPORTED_FORMATS:
                    # 验证MIME类型
                    mime_type, _ = mimetypes.guess_type(file_path)
                    if mime_type and mime_type.startswith('video/'):
                        video_files.append(file_path)
        
        return video_files
    
    def _extract_video_metadata(self, file_path: str) -> Optional[Dict]:
        """提取视频元数据"""
        try:
            # 获取文件基本信息
            file_stat = os.stat(file_path)
            file_size = file_stat.st_size
            
            if FFMPEG_AVAILABLE:
                # 使用ffmpeg提取视频信息
                probe = ffmpeg.probe(file_path)
                video_stream = next(
                    (stream for stream in probe['streams'] if stream['codec_type'] == 'video'),
                    None
                )
                
                if not video_stream:
                    return None
                
                # 提取视频信息
                duration = float(probe['format']['duration'])
                width = int(video_stream['width'])
                height = int(video_stream['height'])
                
                # 计算帧率
                fps = None
                if 'r_frame_rate' in video_stream:
                    fps_str = video_stream['r_frame_rate']
                    if '/' in fps_str:
                        num, den = fps_str.split('/')
                        fps = float(num) / float(den) if float(den) != 0 else None
                
                format_name = probe['format']['format_name']
                
            else:
                # 模拟数据（用于开发测试）
                duration = 120.0  # 2分钟
                width = 1920
                height = 1080
                fps = 30.0
                format_name = "mp4"
            
            return {
                'file_size': file_size,
                'duration': duration,
                'width': width,
                'height': height,
                'fps': fps,
                'format': format_name,
                'file_hash': self._calculate_file_hash(file_path),
                'extracted_at': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            print(f"提取视频元数据失败 {file_path}: {e}")
            return None
    
    def _calculate_file_hash(self, file_path: str, chunk_size: int = 8192) -> str:
        """计算文件哈希值"""
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                # 只读取文件的前1MB来计算哈希，提高性能
                chunk = f.read(min(chunk_size * 128, 1024 * 1024))
                hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception:
            return ""
    
    def get_video_thumbnail(self, video_path: str, timestamp: float = 1.0) -> Optional[str]:
        """生成视频缩略图"""
        if not FFMPEG_AVAILABLE:
            return None
        
        try:
            # 生成缩略图的逻辑
            # 这里可以实现ffmpeg生成缩略图
            pass
        except Exception as e:
            print(f"生成缩略图失败: {e}")
            return None
