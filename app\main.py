"""
FastAPI应用入口文件
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.config.settings import settings
from app.views import project_routes, video_routes, scene_routes, search_routes, subtitle_routes

# 创建FastAPI应用实例
app = FastAPI(
    title="Video Scene Backend",
    description="智能视频场次分割后端系统",
    version="0.1.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 配置CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该配置具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(project_routes.router, prefix="/api/v1", tags=["projects"])
app.include_router(video_routes.router, prefix="/api/v1", tags=["videos"])
app.include_router(scene_routes.router, prefix="/api/v1", tags=["scenes"])
app.include_router(subtitle_routes.router, prefix="/api/v1", tags=["subtitles"])
app.include_router(search_routes.router, prefix="/api/v1", tags=["search"])

@app.get("/")
async def root():
    """根路径健康检查"""
    return {
        "message": "Video Scene Backend API",
        "version": "0.1.0",
        "status": "running"
    }

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )
