"""
搜索控制器
"""
from typing import List, Dict, Any, Optional
from uuid import UUID
from sqlalchemy.orm import Session

from app.services.search_service import SearchService

class SearchController:
    """搜索控制器"""
    
    def __init__(self, db: Session):
        self.db = db
        self.search_service = SearchService(db)
    
    async def search_scenes(self, query: str, user_id: UUID, 
                           project_id: Optional[UUID] = None,
                           limit: int = 20) -> Dict[str, Any]:
        """搜索场次"""
        try:
            results = await self.search_service.hybrid_search_scenes(
                query, user_id, project_id, limit
            )
            
            # 格式化返回结果
            formatted_results = []
            for result in results:
                scene = result["scene"]
                formatted_results.append({
                    "scene_id": str(scene.id),
                    "video_id": str(scene.video_id),
                    "project_id": str(scene.video.project_id),
                    "title": scene.title,
                    "description": scene.description,
                    "start_time": float(scene.start_time),
                    "end_time": float(scene.end_time),
                    "characters": scene.characters or [],
                    "tags": scene.tags or [],
                    "location": scene.location,
                    "mood": scene.mood,
                    "action_type": scene.action_type,
                    "search_score": result.get("final_score", result.get("score", 0)),
                    "search_type": result.get("search_type", "unknown"),
                    "match_type": result.get("match_type", "unknown"),
                    "match_content": result.get("match_content", ""),
                    "ai_generated": scene.ai_generated,
                    "human_verified": scene.human_verified,
                    "created_at": scene.created_at.isoformat(),
                    "video_filename": scene.video.filename if scene.video else None,
                    "project_name": scene.video.project.name if scene.video and scene.video.project else None
                })
            
            return {
                "query": query,
                "total_results": len(formatted_results),
                "results": formatted_results
            }
            
        except Exception as e:
            return {
                "query": query,
                "total_results": 0,
                "results": [],
                "error": str(e)
            }
    
    async def search_subtitles(self, query: str, user_id: UUID,
                              video_id: Optional[UUID] = None,
                              project_id: Optional[UUID] = None,
                              limit: int = 50) -> Dict[str, Any]:
        """搜索字幕"""
        try:
            results = await self.search_service.search_subtitles(
                query, user_id, video_id, project_id, limit
            )
            
            # 格式化返回结果
            formatted_results = []
            for result in results:
                subtitle = result["subtitle"]
                formatted_results.append({
                    "subtitle_id": str(subtitle.id),
                    "video_id": str(subtitle.video_id),
                    "project_id": str(subtitle.video.project_id) if subtitle.video and subtitle.video.project else None,
                    "start_time": float(subtitle.start_time),
                    "end_time": float(subtitle.end_time),
                    "text": subtitle.text,
                    "speaker": subtitle.speaker,
                    "confidence": subtitle.confidence,
                    "language": subtitle.language,
                    "source": subtitle.source,
                    "search_score": result.get("score", 0),
                    "match_content": result.get("match_content", ""),
                    "created_at": subtitle.created_at.isoformat(),
                    "video_filename": subtitle.video.filename if subtitle.video else None,
                    "project_name": subtitle.video.project.name if subtitle.video and subtitle.video.project else None
                })
            
            return {
                "query": query,
                "total_results": len(formatted_results),
                "results": formatted_results
            }
            
        except Exception as e:
            return {
                "query": query,
                "total_results": 0,
                "results": [],
                "error": str(e)
            }
    
    async def get_search_suggestions(self, query: str, user_id: UUID,
                                    project_id: Optional[UUID] = None,
                                    limit: int = 10) -> Dict[str, Any]:
        """获取搜索建议"""
        try:
            suggestions = await self.search_service.get_search_suggestions(
                query, user_id, project_id, limit
            )
            
            return {
                "query": query,
                "suggestions": suggestions
            }
            
        except Exception as e:
            return {
                "query": query,
                "suggestions": [],
                "error": str(e)
            }
    
    async def advanced_search_scenes(self, filters: Dict[str, Any], user_id: UUID,
                                    project_id: Optional[UUID] = None,
                                    limit: int = 50) -> Dict[str, Any]:
        """高级搜索场次"""
        try:
            from app.models.scene import Scene
            from app.models.video import Video
            from app.models.project import Project
            from sqlalchemy import and_, or_
            
            # 构建基础查询
            base_query = self.db.query(Scene).join(Video).join(Project).filter(
                and_(
                    Project.user_id == user_id,
                    Project.status == 'active'
                )
            )
            
            if project_id:
                base_query = base_query.filter(Project.id == project_id)
            
            # 应用过滤条件
            conditions = []
            
            # 时间范围过滤
            if filters.get("start_time_min") is not None:
                conditions.append(Scene.start_time >= filters["start_time_min"])
            if filters.get("start_time_max") is not None:
                conditions.append(Scene.start_time <= filters["start_time_max"])
            if filters.get("duration_min") is not None:
                conditions.append((Scene.end_time - Scene.start_time) >= filters["duration_min"])
            if filters.get("duration_max") is not None:
                conditions.append((Scene.end_time - Scene.start_time) <= filters["duration_max"])
            
            # 人物过滤
            if filters.get("characters"):
                for character in filters["characters"]:
                    conditions.append(Scene.characters.contains([character]))
            
            # 标签过滤
            if filters.get("tags"):
                for tag in filters["tags"]:
                    conditions.append(Scene.tags.contains([tag]))
            
            # 地点过滤
            if filters.get("location"):
                conditions.append(Scene.location.ilike(f"%{filters['location']}%"))
            
            # 情绪过滤
            if filters.get("mood"):
                conditions.append(Scene.mood.ilike(f"%{filters['mood']}%"))
            
            # 动作类型过滤
            if filters.get("action_type"):
                conditions.append(Scene.action_type.ilike(f"%{filters['action_type']}%"))
            
            # AI生成过滤
            if filters.get("ai_generated") is not None:
                conditions.append(Scene.ai_generated == filters["ai_generated"])
            
            # 人工验证过滤
            if filters.get("human_verified") is not None:
                conditions.append(Scene.human_verified == filters["human_verified"])
            
            # 置信度过滤
            if filters.get("confidence_min") is not None:
                conditions.append(Scene.confidence_score >= filters["confidence_min"])
            if filters.get("confidence_max") is not None:
                conditions.append(Scene.confidence_score <= filters["confidence_max"])
            
            # 应用所有条件
            if conditions:
                final_query = base_query.filter(and_(*conditions))
            else:
                final_query = base_query
            
            # 排序
            sort_by = filters.get("sort_by", "start_time")
            sort_order = filters.get("sort_order", "asc")
            
            if sort_by == "start_time":
                if sort_order == "desc":
                    final_query = final_query.order_by(Scene.start_time.desc())
                else:
                    final_query = final_query.order_by(Scene.start_time.asc())
            elif sort_by == "created_at":
                if sort_order == "desc":
                    final_query = final_query.order_by(Scene.created_at.desc())
                else:
                    final_query = final_query.order_by(Scene.created_at.asc())
            elif sort_by == "confidence_score":
                if sort_order == "desc":
                    final_query = final_query.order_by(Scene.confidence_score.desc())
                else:
                    final_query = final_query.order_by(Scene.confidence_score.asc())
            
            # 执行查询
            scenes = final_query.limit(limit).all()
            
            # 格式化结果
            formatted_results = []
            for scene in scenes:
                formatted_results.append({
                    "scene_id": str(scene.id),
                    "video_id": str(scene.video_id),
                    "project_id": str(scene.video.project_id),
                    "title": scene.title,
                    "description": scene.description,
                    "start_time": float(scene.start_time),
                    "end_time": float(scene.end_time),
                    "duration": float(scene.end_time - scene.start_time),
                    "characters": scene.characters or [],
                    "tags": scene.tags or [],
                    "location": scene.location,
                    "mood": scene.mood,
                    "action_type": scene.action_type,
                    "ai_generated": scene.ai_generated,
                    "human_verified": scene.human_verified,
                    "confidence_score": float(scene.confidence_score) if scene.confidence_score else None,
                    "created_at": scene.created_at.isoformat(),
                    "video_filename": scene.video.filename if scene.video else None,
                    "project_name": scene.video.project.name if scene.video and scene.video.project else None
                })
            
            return {
                "filters": filters,
                "total_results": len(formatted_results),
                "results": formatted_results
            }
            
        except Exception as e:
            return {
                "filters": filters,
                "total_results": 0,
                "results": [],
                "error": str(e)
            }
